/**
 * User Feedback Page
 * Allows users to submit feedback and view their feedback history
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  AlertTriangle,
  Bug,
  CheckCircle,
  Clock,
  Frown,
  HelpCircle,
  Lightbulb,
  MessageSquare,
  Plus,
  Smile,
  Star,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { createDbFromEnv } from "~/core/db";
import {
  createFeedback,
  type FeedbackPriority,
  type FeedbackType,
  type FeedbackWithUser,
  getFeedback,
} from "~/core/services/feedback.server";
import { getUserUuid } from "~/core/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "10", 10);

    const feedbackData = await getFeedback(
      {
        page,
        limit,
        userUuid,
      },
      db
    );

    return json({
      success: true,
      data: {
        feedback: feedbackData.feedback,
        pagination: feedbackData.pagination,
      },
    });
  } catch (error) {
    console.error("Error loading feedback:", error);
    return json({ success: false, error: "Failed to load feedback" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const formData = await request.formData();
    const action = formData.get("action") as string;

    if (action === "create") {
      const title = formData.get("title") as string;
      const content = formData.get("content") as string;
      const type = formData.get("type") as FeedbackType;
      const priority = formData.get("priority") as FeedbackPriority;
      const rating = formData.get("rating") as string;

      const result = await createFeedback(
        {
          userUuid,
          title,
          content,
          type,
          priority,
          rating: rating ? parseInt(rating, 10) : undefined,
        },
        db
      );

      if (result.success) {
        return json({
          success: true,
          message: "Feedback submitted successfully! We'll review it and get back to you.",
        });
      } else {
        return json({
          success: false,
          error: result.error,
        });
      }
    }

    return json({ success: false, error: "Invalid action" });
  } catch (error) {
    console.error("Error processing feedback action:", error);
    return json({ success: false, error: "Failed to process request" }, { status: 500 });
  }
}

export default function FeedbackPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const [showForm, setShowForm] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number>(0);

  const isSubmitting = navigation.state === "submitting";

  const feedbackTypes = [
    { value: "bug_report", label: "Bug Report", icon: Bug, color: "text-red-500" },
    { value: "feature_request", label: "Feature Request", icon: Lightbulb, color: "text-blue-500" },
    {
      value: "general_feedback",
      label: "General Feedback",
      icon: MessageSquare,
      color: "text-gray-500",
    },
    {
      value: "support_request",
      label: "Support Request",
      icon: HelpCircle,
      color: "text-purple-500",
    },
    { value: "complaint", label: "Complaint", icon: Frown, color: "text-red-500" },
    { value: "compliment", label: "Compliment", icon: Smile, color: "text-green-500" },
    { value: "suggestion", label: "Suggestion", icon: Zap, color: "text-yellow-500" },
  ];

  const priorityLevels = [
    {
      value: "low",
      label: "Low",
      color: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    },
    {
      value: "medium",
      label: "Medium",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    },
    {
      value: "high",
      label: "High",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    },
    {
      value: "urgent",
      label: "Urgent",
      color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "resolved":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      case "duplicate":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <Clock className="w-4 h-4" />;
      case "in_progress":
        return <AlertTriangle className="w-4 h-4" />;
      case "resolved":
        return <CheckCircle className="w-4 h-4" />;
      case "closed":
        return <XCircle className="w-4 h-4" />;
      case "duplicate":
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    const typeInfo = feedbackTypes.find((t) => t.value === type);
    if (typeInfo) {
      const Icon = typeInfo.icon;
      return <Icon className={`w-4 h-4 ${typeInfo.color}`} />;
    }
    return <MessageSquare className="w-4 h-4 text-gray-500" />;
  };

  const getPriorityColor = (priority: string) => {
    const priorityInfo = priorityLevels.find((p) => p.value === priority);
    return (
      priorityInfo?.color || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
    );
  };

  const renderStars = (rating: number, interactive = false) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-5 h-5 ${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            } ${interactive ? "cursor-pointer hover:text-yellow-400" : ""}`}
            onClick={interactive ? () => setSelectedRating(star) : undefined}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Feedback</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Share your thoughts and help us improve our service
              </p>
            </div>
            <Button onClick={() => setShowForm(true)} disabled={isSubmitting}>
              <Plus className="w-4 h-4 mr-2" />
              Submit Feedback
            </Button>
          </div>
        </div>

        {/* Action Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        {/* Feedback Form */}
        {showForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Submit New Feedback</CardTitle>
              <CardDescription>
                Help us improve by sharing your experience, reporting bugs, or suggesting new
                features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-6">
                <input type="hidden" name="action" value="create" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Feedback Type</Label>
                    <Select name="type" defaultValue="general_feedback">
                      <SelectTrigger>
                        <SelectValue placeholder="Select feedback type" />
                      </SelectTrigger>
                      <SelectContent>
                        {feedbackTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center space-x-2">
                              <type.icon className={`w-4 h-4 ${type.color}`} />
                              <span>{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select name="priority" defaultValue="medium">
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        {priorityLevels.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            {priority.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="Brief summary of your feedback"
                    required
                    maxLength={200}
                  />
                </div>

                <div>
                  <Label htmlFor="content">Description</Label>
                  <Textarea
                    id="content"
                    name="content"
                    placeholder="Please provide detailed information about your feedback..."
                    required
                    rows={6}
                    maxLength={5000}
                  />
                </div>

                <div>
                  <Label>Rating (Optional)</Label>
                  <div className="mt-2">
                    {renderStars(selectedRating, true)}
                    <input type="hidden" name="rating" value={selectedRating || ""} />
                    <p className="text-sm text-gray-500 mt-1">
                      Rate your overall experience (optional)
                    </p>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Submitting..." : "Submit Feedback"}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                    Cancel
                  </Button>
                </div>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* Feedback History */}
        <Card>
          <CardHeader>
            <CardTitle>Your Feedback History</CardTitle>
            <CardDescription>
              {data.feedback.length === 0
                ? "No feedback submitted yet"
                : `${data.feedback.length} feedback item${data.feedback.length === 1 ? "" : "s"} found`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.feedback.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No Feedback Yet</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Share your thoughts and help us improve our service
                </p>
                <Button onClick={() => setShowForm(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Submit Your First Feedback
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {data.feedback.map((feedback: FeedbackWithUser) => (
                  <div
                    key={feedback.id}
                    className="p-4 border rounded-lg bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {feedback.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(feedback.type)}
                            <Badge className={getStatusColor(feedback.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(feedback.status)}
                                <span className="capitalize">
                                  {feedback.status.replace("_", " ")}
                                </span>
                              </div>
                            </Badge>
                            <Badge className={getPriorityColor(feedback.priority)}>
                              {feedback.priority}
                            </Badge>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-400 mb-3">{feedback.content}</p>

                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center space-x-4">
                            <span>
                              Submitted {new Date(feedback.createdAt).toLocaleDateString()}
                            </span>
                            {feedback.rating && (
                              <div className="flex items-center space-x-1">
                                <span>Rating:</span>
                                {renderStars(feedback.rating)}
                              </div>
                            )}
                          </div>
                          {feedback.resolvedAt && (
                            <span>
                              Resolved {new Date(feedback.resolvedAt).toLocaleDateString()}
                            </span>
                          )}
                        </div>

                        {feedback.adminNotes && (
                          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-400">
                            <p className="text-sm font-medium text-blue-900 dark:text-blue-400 mb-1">
                              Admin Response:
                            </p>
                            <p className="text-sm text-blue-800 dark:text-blue-300">
                              {feedback.adminNotes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasPrev}>
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Page {data.pagination.currentPage} of {data.pagination.totalPages}
                  </span>
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasNext}>
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
