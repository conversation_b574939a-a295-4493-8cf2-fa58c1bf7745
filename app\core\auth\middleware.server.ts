/**
 * Lucia 3 认证中间件
 */

import { redirect } from "@remix-run/node";
import { lucia } from "./lucia.server";

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  emailVerified: boolean;
  googleId?: string;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  error?: string;
}

/**
 * 核心认证函数 - requireUser
 * 验证 Lucia 3 会话并返回用户信息，如果未认证则重定向到登录页
 */
export async function requireUser(request: Request): Promise<User> {
  const sessionId = lucia.readSessionCookie(request.headers.get("Cookie") ?? "");
  
  if (!sessionId) {
    throw redirect("/auth/login");
  }

  const { session, user } = await lucia.validateSession(sessionId);
  
  if (!session) {
    throw redirect("/auth/login");
  }

  return user;
}

/**
 * 可选的非强制认证检查
 * 返回用户信息或 null，不会重定向
 */
export async function getUser(request: Request): Promise<AuthResult> {
  try {
    const sessionId = lucia.readSessionCookie(request.headers.get("Cookie") ?? "");
    
    if (!sessionId) {
      return { success: false, error: "No session found" };
    }

    const { session, user } = await lucia.validateSession(sessionId);
    
    if (!session) {
      return { success: false, error: "Invalid session" };
    }

    return { success: true, user };
  } catch (error) {
    console.error("Auth check error:", error);
    return { success: false, error: "Authentication failed" };
  }
}

/**
 * API专用认证函数 - 返回JSON错误而不是重定向
 * 用于API路由，不会重定向到登录页面
 */
export async function requireUserForAPI(request: Request): Promise<User> {
  const sessionId = lucia.readSessionCookie(request.headers.get("Cookie") ?? "");

  if (!sessionId) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Authentication required",
        message: "Please log in to access this resource",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  const { session, user } = await lucia.validateSession(sessionId);

  if (!session) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Invalid authentication",
        message: "Your session has expired. Please log in again",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  return user;
}
