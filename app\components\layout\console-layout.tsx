import { Link } from "@remix-run/react";
import { <PERSON> } from "lucide-react";
import type { ReactNode } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { createConsoleSidebarConfig } from "~/config/navigation";
import type { UserInfo } from "./base-sidebar-layout";
import BaseSidebarLayout from "./base-sidebar-layout";

export interface ConsoleLayoutProps {
  children: ReactNode;
  className?: string;
  user?: UserInfo;
}

export default function ConsoleLayout({ children, className = "", user }: ConsoleLayoutProps) {
  // Default user data (in a real app, this would come from props or context)
  const defaultUser: UserInfo = user || {
    name: "<PERSON>",
    initials: "J<PERSON>",
    plan: "Pro Plan",
    credits: 2847,
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  };

  const sidebarConfig = createConsoleSidebarConfig(defaultUser);

  const topBarActions = (
    <>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="outline" size="sm" asChild>
        <Link to="/ai-tools">AI Tools</Link>
      </Button>
    </>
  );

  return (
    <BaseSidebarLayout
      sidebarConfig={sidebarConfig}
      topBarActions={topBarActions}
      className={className}
    >
      {children}
    </BaseSidebarLayout>
  );
}
