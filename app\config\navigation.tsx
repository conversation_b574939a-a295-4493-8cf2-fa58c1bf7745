/**
 * Navigation Configuration
 * Centralized navigation items for console and admin layouts
 */

import {
  BarChart3,
  Bell,
  Bot,
  CreditCard,
  Database,
  FileText,
  HelpCircle,
  Key,
  LogOut,
  MessageSquare,
  Settings,
  Shield,
  ShoppingBag,
  ShoppingCart,
  User,
  Users,
} from "lucide-react";
import type { NavItem, SidebarConfig, UserInfo } from "~/components/layout/base-sidebar-layout";

// Console Navigation
export const getConsoleNavigation = (): NavItem[] => [
  {
    title: "Overview",
    url: "/console",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "API Keys",
    url: "/console/api-keys",
    icon: <Key className="h-5 w-5" />,
  },
  {
    title: "Credits",
    url: "/console/credits",
    icon: <CreditCard className="h-5 w-5" />,
  },
  {
    title: "Usage",
    url: "/console/usage",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Orders",
    url: "/console/orders",
    icon: <ShoppingBag className="h-5 w-5" />,
  },
  {
    title: "Profile",
    url: "/console/profile",
    icon: <User className="h-5 w-5" />,
  },
  {
    title: "Settings",
    url: "/console/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

export const getConsoleFooterActions = (): NavItem[] => [
  {
    title: "Help & Support",
    url: "/help",
    icon: <HelpCircle className="h-4 w-4 mr-3" />,
  },
  {
    title: "Sign Out",
    url: "/auth/logout",
    icon: <LogOut className="h-4 w-4 mr-3" />,
  },
];

// Admin Navigation
export const getAdminNavigation = (): NavItem[] => [
  {
    title: "Dashboard",
    url: "/admin",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Users",
    url: "/admin/users",
    icon: <Users className="h-5 w-5" />,
    badge: "124",
  },
  {
    title: "Orders",
    url: "/admin/orders",
    icon: <ShoppingCart className="h-5 w-5" />,
    badge: "23",
  },
  {
    title: "Analytics",
    url: "/admin/analytics",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Feedback",
    url: "/admin/feedback",
    icon: <MessageSquare className="h-5 w-5" />,
    badge: "5",
  },
  {
    title: "Security",
    url: "/admin/security",
    icon: <Shield className="h-5 w-5" />,
  },
  {
    title: "Performance",
    url: "/admin/performance",
    icon: <Database className="h-5 w-5" />,
  },
  {
    title: "Deployment",
    url: "/admin/deployment",
    icon: <Bot className="h-5 w-5" />,
  },
  {
    title: "Monitoring",
    url: "/admin/monitoring",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    title: "Settings",
    url: "/admin/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

export const getAdminFooterActions = (): NavItem[] => [
  {
    title: "Documentation",
    url: "/docs",
    icon: <FileText className="h-4 w-4 mr-3" />,
  },
  {
    title: "Sign Out",
    url: "/auth/logout",
    icon: <LogOut className="h-4 w-4 mr-3" />,
  },
];

// Configuration builders
export const createConsoleSidebarConfig = (user?: UserInfo): SidebarConfig => ({
  title: "Console",
  basePath: "/console",
  navigation: getConsoleNavigation(),
  footerActions: getConsoleFooterActions(),
  user,
  logo: (
    <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
      <User className="h-5 w-5 text-primary-foreground" />
    </div>
  ),
});

export const createAdminSidebarConfig = (user?: UserInfo): SidebarConfig => ({
  title: "Admin",
  basePath: "/admin",
  navigation: getAdminNavigation(),
  footerActions: getAdminFooterActions(),
  user,
  logo: (
    <div className="w-8 h-8 bg-destructive rounded-lg flex items-center justify-center">
      <Shield className="h-5 w-5 text-destructive-foreground" />
    </div>
  ),
});

// Top bar action components for different layouts
export const getConsoleTopBarActions = () => (
  <>
    <button type="button" className="relative p-2 text-muted-foreground hover:text-foreground">
      <Bell className="h-5 w-5" />
      <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full" />
    </button>
  </>
);

export const getAdminTopBarActions = () => (
  <>
    <button type="button" className="relative p-2 text-muted-foreground hover:text-foreground">
      <Bell className="h-5 w-5" />
      <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full" />
    </button>
    <button type="button" className="p-2 text-muted-foreground hover:text-foreground">
      <Settings className="h-5 w-5" />
    </button>
  </>
);
