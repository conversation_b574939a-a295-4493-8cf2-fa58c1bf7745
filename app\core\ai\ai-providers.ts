import { deepseek } from "@ai-sdk/deepseek";
import { openai } from "@ai-sdk/openai";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { replicate } from "@ai-sdk/replicate";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import type { LanguageModelV1 } from "ai";
import { extractReasoningMiddleware, wrapLanguageModel } from "ai";

/**
 * AI Provider types
 */
export type AIProvider = "openai" | "deepseek" | "openrouter" | "siliconflow" | "replicate";

/**
 * AI Provider configuration
 */
export interface AIProviderConfig {
  name: string;
  displayName: string;
  models: string[];
  requiresApiKey: boolean;
  supportsReasoning?: boolean;
  reasoningModels?: string[];
  supportsEmbeddings?: boolean;
  supportsImageClassification?: boolean;
}

/**
 * Available AI providers configuration
 */
export const AI_PROVIDERS: Record<AIProvider, AIProviderConfig> = {
  openai: {
    name: "openai",
    displayName: "OpenAI",
    models: ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo", "o1-preview", "o1-mini"],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["o1-preview", "o1-mini"],
  },
  deepseek: {
    name: "deepseek",
    displayName: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder", "deepseek-r1"],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek-r1"],
  },
  openrouter: {
    name: "openrouter",
    displayName: "OpenRouter",
    models: [
      "deepseek/deepseek-r1",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
    ],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek/deepseek-r1"],
  },
  siliconflow: {
    name: "siliconflow",
    displayName: "SiliconFlow",
    models: [
      "deepseek-ai/DeepSeek-R1",
      "Qwen/Qwen2.5-72B-Instruct",
      "meta-llama/Meta-Llama-3.1-405B-Instruct",
    ],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek-ai/DeepSeek-R1"],
  },
  replicate: {
    name: "replicate",
    displayName: "Replicate",
    models: ["meta/llama-2-70b-chat", "mistralai/mixtral-8x7b-instruct-v0.1"],
    requiresApiKey: true,
  },
};

/**
 * Environment variables interface for AI providers
 */
export interface AIEnvironmentVariables {
  OPENAI_API_KEY?: string;
  DEEPSEEK_API_KEY?: string;
  OPENROUTER_API_KEY?: string;
  SILICONFLOW_API_KEY?: string;
  SILICONFLOW_BASE_URL?: string;
  REPLICATE_API_TOKEN?: string;
}

/**
 * Create AI model instance based on provider and model
 */
export function createAIModel(
  provider: AIProvider,
  model: string,
  env: AIEnvironmentVariables
): LanguageModelV1 {
  let textModel: LanguageModelV1;

  switch (provider) {
    case "openai": {
      if (!env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY is required for OpenAI provider");
      }
      textModel = openai(model, {
        apiKey: env.OPENAI_API_KEY,
      });
      break;
    }

    case "deepseek": {
      if (!env.DEEPSEEK_API_KEY) {
        throw new Error("DEEPSEEK_API_KEY is required for DeepSeek provider");
      }
      textModel = deepseek(model, {
        apiKey: env.DEEPSEEK_API_KEY,
      });
      break;
    }

    case "openrouter": {
      if (!env.OPENROUTER_API_KEY) {
        throw new Error("OPENROUTER_API_KEY is required for OpenRouter provider");
      }
      const openrouter = createOpenRouter({
        apiKey: env.OPENROUTER_API_KEY,
      });
      textModel = openrouter(model);

      // Add reasoning middleware for supported models
      if (model === "deepseek/deepseek-r1") {
        textModel = wrapLanguageModel({
          model: textModel,
          middleware: extractReasoningMiddleware({
            tagName: "think",
          }),
        });
      }
      break;
    }

    case "siliconflow": {
      if (!env.SILICONFLOW_API_KEY) {
        throw new Error("SILICONFLOW_API_KEY is required for SiliconFlow provider");
      }
      const siliconflow = createOpenAICompatible({
        name: "siliconflow",
        apiKey: env.SILICONFLOW_API_KEY,
        baseURL: env.SILICONFLOW_BASE_URL || "https://api.siliconflow.cn/v1",
      });
      textModel = siliconflow(model);

      // Add reasoning middleware for supported models
      if (model === "deepseek-ai/DeepSeek-R1") {
        textModel = wrapLanguageModel({
          model: textModel,
          middleware: extractReasoningMiddleware({
            tagName: "reasoning_content",
          }),
        });
      }
      break;
    }

    case "replicate": {
      if (!env.REPLICATE_API_TOKEN) {
        throw new Error("REPLICATE_API_TOKEN is required for Replicate provider");
      }
      textModel = replicate(model, {
        apiToken: env.REPLICATE_API_TOKEN,
      });
      break;
    }

    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }

  return textModel;
}

/**
 * Validate if provider and model combination is supported
 */
export function validateProviderModel(provider: AIProvider, model: string): boolean {
  const providerConfig = AI_PROVIDERS[provider];
  if (!providerConfig) {
    return false;
  }
  return providerConfig.models.includes(model);
}

/**
 * Get available models for a provider
 */
export function getProviderModels(provider: AIProvider): string[] {
  const providerConfig = AI_PROVIDERS[provider];
  return providerConfig?.models || [];
}

/**
 * Check if model supports reasoning
 */
export function supportsReasoning(provider: AIProvider, model: string): boolean {
  const providerConfig = AI_PROVIDERS[provider];
  if (!providerConfig.supportsReasoning) {
    return false;
  }
  return providerConfig.reasoningModels?.includes(model) || false;
}

/**
 * Get all available providers
 */
export function getAvailableProviders(): AIProviderConfig[] {
  return Object.values(AI_PROVIDERS);
}
