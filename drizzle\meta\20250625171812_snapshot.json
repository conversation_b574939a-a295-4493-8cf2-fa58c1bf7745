{"id": "33d9b81e-805a-4b68-826d-06eebc602b17", "prevId": "db8d2f53-911e-4e2a-8412-8be36f06fb6b", "version": "7", "dialect": "postgresql", "tables": {"public.affiliates": {"name": "affiliates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "commission_rate": {"name": "commission_rate", "type": "numeric(5, 4)", "primaryKey": false, "notNull": true, "default": "'0.1000'"}, "total_earnings": {"name": "total_earnings", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"affiliates_user_idx": {"name": "affiliates_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "affiliates_code_idx": {"name": "affiliates_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "affiliates_active_idx": {"name": "affiliates_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"affiliates_user_id_users_id_fk": {"name": "affiliates_user_id_users_id_fk", "tableFrom": "affiliates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"affiliates_code_unique": {"name": "affiliates_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_customers": {"name": "billing_customers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"billing_customers_user_idx": {"name": "billing_customers_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "billing_customers_provider_customer_idx": {"name": "billing_customers_provider_customer_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"billing_customers_user_id_users_id_fk": {"name": "billing_customers_user_id_users_id_fk", "tableFrom": "billing_customers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "trans_no": {"name": "trans_no", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"credit_transactions_user_idx": {"name": "credit_transactions_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_trans_no_idx": {"name": "credit_transactions_trans_no_idx", "columns": [{"expression": "trans_no", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_type_idx": {"name": "credit_transactions_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_order_idx": {"name": "credit_transactions_order_idx", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_created_idx": {"name": "credit_transactions_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"credit_transactions_user_id_users_id_fk": {"name": "credit_transactions_user_id_users_id_fk", "tableFrom": "credit_transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credit_transactions_order_id_orders_id_fk": {"name": "credit_transactions_order_id_orders_id_fk", "tableFrom": "credit_transactions", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"credit_transactions_trans_no_unique": {"name": "credit_transactions_trans_no_unique", "nullsNotDistinct": false, "columns": ["trans_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price_id": {"name": "price_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "unit_price": {"name": "unit_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"order_items_order_idx": {"name": "order_items_order_idx", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "order_items_product_idx": {"name": "order_items_product_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "affiliate_id": {"name": "affiliate_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order_number": {"name": "order_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax": {"name": "tax", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "total": {"name": "total", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "provider": {"name": "provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "billing_provider": {"name": "billing_provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "provider_order_id": {"name": "provider_order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_uuid": {"name": "user_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"orders_user_idx": {"name": "orders_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_status_idx": {"name": "orders_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_affiliate_idx": {"name": "orders_affiliate_idx", "columns": [{"expression": "affiliate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_number_idx": {"name": "orders_number_idx", "columns": [{"expression": "order_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_order_no_idx": {"name": "orders_order_no_idx", "columns": [{"expression": "order_no", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_provider_idx": {"name": "orders_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_user_email_idx": {"name": "orders_user_email_idx", "columns": [{"expression": "user_email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_user_uuid_idx": {"name": "orders_user_uuid_idx", "columns": [{"expression": "user_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_user_id_users_id_fk": {"name": "orders_user_id_users_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_affiliate_id_affiliates_id_fk": {"name": "orders_affiliate_id_affiliates_id_fk", "tableFrom": "orders", "tableTo": "affiliates", "columnsFrom": ["affiliate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_number_unique": {"name": "orders_order_number_unique", "nullsNotDistinct": false, "columns": ["order_number"]}, "orders_order_no_unique": {"name": "orders_order_no_unique", "nullsNotDistinct": false, "columns": ["order_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_items": {"name": "subscription_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "subscription_item_id": {"name": "subscription_item_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price_id": {"name": "price_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscription_items_subscription_idx": {"name": "subscription_items_subscription_idx", "columns": [{"expression": "subscription_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_items_item_idx": {"name": "subscription_items_item_idx", "columns": [{"expression": "subscription_item_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_items_price_idx": {"name": "subscription_items_price_idx", "columns": [{"expression": "price_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_items_product_idx": {"name": "subscription_items_product_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subscription_items_subscription_id_subscriptions_id_fk": {"name": "subscription_items_subscription_id_subscriptions_id_fk", "tableFrom": "subscription_items", "tableTo": "subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "billing_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "subscription_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "current_period_start": {"name": "current_period_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_starts_at": {"name": "period_starts_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_ends_at": {"name": "period_ends_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "canceled_at": {"name": "canceled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_start": {"name": "trial_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_end": {"name": "trial_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscriptions_user_idx": {"name": "subscriptions_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_subscription_idx": {"name": "subscriptions_subscription_idx", "columns": [{"expression": "subscription_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_status_idx": {"name": "subscriptions_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_active_idx": {"name": "subscriptions_active_idx", "columns": [{"expression": "active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscriptions_period_end_idx": {"name": "subscriptions_period_end_idx", "columns": [{"expression": "current_period_end", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subscriptions_user_id_users_id_fk": {"name": "subscriptions_user_id_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"conversations_user_idx": {"name": "conversations_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_archived_idx": {"name": "conversations_archived_idx", "columns": [{"expression": "is_archived", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_last_message_idx": {"name": "conversations_last_message_idx", "columns": [{"expression": "last_message_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_model_idx": {"name": "conversations_model_idx", "columns": [{"expression": "model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_provider_idx": {"name": "conversations_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"conversations_user_id_users_id_fk": {"name": "conversations_user_id_users_id_fk", "tableFrom": "conversations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generated_images": {"name": "generated_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": false}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"generated_images_user_idx": {"name": "generated_images_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_images_message_idx": {"name": "generated_images_message_idx", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_images_model_idx": {"name": "generated_images_model_idx", "columns": [{"expression": "model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_images_provider_idx": {"name": "generated_images_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_images_created_idx": {"name": "generated_images_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"generated_images_user_id_users_id_fk": {"name": "generated_images_user_id_users_id_fk", "tableFrom": "generated_images", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "generated_images_message_id_messages_id_fk": {"name": "generated_images_message_id_messages_id_fk", "tableFrom": "generated_images", "tableTo": "messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"messages_conversation_idx": {"name": "messages_conversation_idx", "columns": [{"expression": "conversation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_role_idx": {"name": "messages_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_created_idx": {"name": "messages_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_model_idx": {"name": "messages_model_idx", "columns": [{"expression": "model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_provider_idx": {"name": "messages_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "feedback_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "feedback_priority", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'medium'"}, "status": {"name": "status", "type": "feedback_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false}, "admin_notes": {"name": "admin_notes", "type": "text", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "resolved_by": {"name": "resolved_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"feedback_user_idx": {"name": "feedback_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_type_idx": {"name": "feedback_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_status_idx": {"name": "feedback_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_priority_idx": {"name": "feedback_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_created_idx": {"name": "feedback_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "feedback_email_idx": {"name": "feedback_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"feedback_user_id_users_id_fk": {"name": "feedback_user_id_users_id_fk", "tableFrom": "feedback", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "feedback_resolved_by_users_id_fk": {"name": "feedback_resolved_by_users_id_fk", "tableFrom": "feedback", "tableTo": "users", "columnsFrom": ["resolved_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": false}, "published": {"name": "published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"posts_slug_idx": {"name": "posts_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_author_idx": {"name": "posts_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_published_idx": {"name": "posts_published_idx", "columns": [{"expression": "published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_published_at_idx": {"name": "posts_published_at_idx", "columns": [{"expression": "published_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_author_id_users_id_fk": {"name": "posts_author_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"posts_slug_unique": {"name": "posts_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_usage": {"name": "api_usage", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "api_key_id": {"name": "api_key_id", "type": "uuid", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": true}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "request_size": {"name": "request_size", "type": "integer", "primaryKey": false, "notNull": false}, "response_size": {"name": "response_size", "type": "integer", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "referer": {"name": "referer", "type": "text", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric(10, 6)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"api_usage_user_idx": {"name": "api_usage_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_api_key_idx": {"name": "api_usage_api_key_idx", "columns": [{"expression": "api_key_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_endpoint_idx": {"name": "api_usage_endpoint_idx", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_status_idx": {"name": "api_usage_status_idx", "columns": [{"expression": "status_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_created_idx": {"name": "api_usage_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_model_idx": {"name": "api_usage_model_idx", "columns": [{"expression": "model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_provider_idx": {"name": "api_usage_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_user_endpoint_idx": {"name": "api_usage_user_endpoint_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_usage_user_created_idx": {"name": "api_usage_user_created_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"api_usage_user_id_users_id_fk": {"name": "api_usage_user_id_users_id_fk", "tableFrom": "api_usage", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rate_limits": {"name": "rate_limits", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "identifier_type": {"name": "identifier_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "window_start": {"name": "window_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "window_end": {"name": "window_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "limit_count": {"name": "limit_count", "type": "integer", "primaryKey": false, "notNull": true}, "reset_at": {"name": "reset_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"rate_limits_identifier_idx": {"name": "rate_limits_identifier_idx", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rate_limits_endpoint_idx": {"name": "rate_limits_endpoint_idx", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rate_limits_reset_idx": {"name": "rate_limits_reset_idx", "columns": [{"expression": "reset_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rate_limits_window_idx": {"name": "rate_limits_window_idx", "columns": [{"expression": "window_start", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "window_end", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rate_limits_identifier_endpoint_idx": {"name": "rate_limits_identifier_endpoint_idx", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_metrics": {"name": "system_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metric_name": {"name": "metric_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "metric_value": {"name": "metric_value", "type": "numeric(15, 6)", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"system_metrics_timestamp_idx": {"name": "system_metrics_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_metrics_name_idx": {"name": "system_metrics_name_idx", "columns": [{"expression": "metric_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_metrics_name_timestamp_idx": {"name": "system_metrics_name_timestamp_idx", "columns": [{"expression": "metric_name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage_stats": {"name": "usage_stats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "total_requests": {"name": "total_requests", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "successful_requests": {"name": "successful_requests", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "error_requests": {"name": "error_requests", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "avg_response_time": {"name": "avg_response_time", "type": "integer", "primaryKey": false, "notNull": false}, "total_tokens": {"name": "total_tokens", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_cost": {"name": "total_cost", "type": "numeric(10, 6)", "primaryKey": false, "notNull": true, "default": "'0.000000'"}, "unique_models": {"name": "unique_models", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "unique_providers": {"name": "unique_providers", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_request_size": {"name": "total_request_size", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_response_size": {"name": "total_response_size", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"usage_stats_user_idx": {"name": "usage_stats_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "usage_stats_date_idx": {"name": "usage_stats_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "usage_stats_period_idx": {"name": "usage_stats_period_idx", "columns": [{"expression": "period", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "usage_stats_user_date_idx": {"name": "usage_stats_user_date_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "usage_stats_user_period_idx": {"name": "usage_stats_user_period_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "period", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"usage_stats_user_id_users_id_fk": {"name": "usage_stats_user_id_users_id_fk", "tableFrom": "usage_stats", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_user_id": {"name": "provider_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"accounts_provider_user_idx": {"name": "accounts_provider_user_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accounts_user_id_provider_unique": {"name": "accounts_user_id_provider_unique", "nullsNotDistinct": false, "columns": ["user_id", "provider"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accounts_memberships": {"name": "accounts_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"memberships_user_idx": {"name": "memberships_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "memberships_role_idx": {"name": "memberships_role_idx", "columns": [{"expression": "role_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_memberships_account_id_accounts_id_fk": {"name": "accounts_memberships_account_id_accounts_id_fk", "tableFrom": "accounts_memberships", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "accounts_memberships_user_id_users_id_fk": {"name": "accounts_memberships_user_id_users_id_fk", "tableFrom": "accounts_memberships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "accounts_memberships_role_id_roles_id_fk": {"name": "accounts_memberships_role_id_roles_id_fk", "tableFrom": "accounts_memberships", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "accounts_memberships_invited_by_users_id_fk": {"name": "accounts_memberships_invited_by_users_id_fk", "tableFrom": "accounts_memberships", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accounts_memberships_account_id_user_id_unique": {"name": "accounts_memberships_account_id_user_id_unique", "nullsNotDistinct": false, "columns": ["account_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key_hash": {"name": "key_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"api_keys_user_idx": {"name": "api_keys_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_hash_idx": {"name": "api_keys_hash_idx", "columns": [{"expression": "key_hash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_active_idx": {"name": "api_keys_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_hash_unique": {"name": "api_keys_key_hash_unique", "nullsNotDistinct": false, "columns": ["key_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "accepted_by": {"name": "accepted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"invitations_token_idx": {"name": "invitations_token_idx", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitations_email_idx": {"name": "invitations_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitations_expires_idx": {"name": "invitations_expires_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitations_role_id_roles_id_fk": {"name": "invitations_role_id_roles_id_fk", "tableFrom": "invitations", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_accepted_by_users_id_fk": {"name": "invitations_accepted_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["accepted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitations_token_unique": {"name": "invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.key": {"name": "key", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "hashed_password": {"name": "hashed_password", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"key_user_id_users_id_fk": {"name": "key_user_id_users_id_fk", "tableFrom": "key", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'system'"}, "channel": {"name": "channel", "type": "notification_channel", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'in_app'"}, "link": {"name": "link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "dismissed": {"name": "dismissed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"notifications_user_idx": {"name": "notifications_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_type_idx": {"name": "notifications_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_channel_idx": {"name": "notifications_channel_idx", "columns": [{"expression": "channel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_dismissed_idx": {"name": "notifications_dismissed_idx", "columns": [{"expression": "dismissed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_read_idx": {"name": "notifications_read_idx", "columns": [{"expression": "read_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_expires_idx": {"name": "notifications_expires_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_created_idx": {"name": "notifications_created_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"role_id": {"name": "role_id", "type": "integer", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "app_permissions", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"role_permissions_role_id_permission_pk": {"name": "role_permissions_role_id_permission_pk", "columns": ["role_id", "permission"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"roles_name_unique": {"name": "roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "invite_code": {"name": "invite_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "text", "primaryKey": false, "notNull": false}, "theme": {"name": "theme", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'system'"}, "signin_type": {"name": "signin_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "signin_provider": {"name": "signin_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "signin_openid": {"name": "signin_openid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "signin_ip": {"name": "signin_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "google_sub": {"name": "google_sub", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": false}, "token_version": {"name": "token_version", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_affiliate": {"name": "is_affiliate", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_uuid_idx": {"name": "users_uuid_idx", "columns": [{"expression": "uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_invite_code_idx": {"name": "users_invite_code_idx", "columns": [{"expression": "invite_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_invited_by_idx": {"name": "users_invited_by_idx", "columns": [{"expression": "invited_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_signin_provider_idx": {"name": "users_signin_provider_idx", "columns": [{"expression": "signin_provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_signin_openid_idx": {"name": "users_signin_openid_idx", "columns": [{"expression": "signin_openid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_google_sub_idx": {"name": "users_google_sub_idx", "columns": [{"expression": "google_sub", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_invited_by_users_id_fk": {"name": "users_invited_by_users_id_fk", "tableFrom": "users", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_uuid_unique": {"name": "users_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_invite_code_unique": {"name": "users_invite_code_unique", "nullsNotDistinct": false, "columns": ["invite_code"]}, "users_google_sub_unique": {"name": "users_google_sub_unique", "nullsNotDistinct": false, "columns": ["google_sub"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.billing_provider": {"name": "billing_provider", "schema": "public", "values": ["stripe", "lemon-squeezy", "paddle"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "succeeded", "failed"]}, "public.subscription_status": {"name": "subscription_status", "schema": "public", "values": ["active", "trialing", "past_due", "canceled", "unpaid", "incomplete", "incomplete_expired", "paused"]}, "public.feedback_priority": {"name": "feedback_priority", "schema": "public", "values": ["low", "medium", "high", "urgent"]}, "public.feedback_status": {"name": "feedback_status", "schema": "public", "values": ["open", "in_progress", "resolved", "closed", "duplicate"]}, "public.feedback_type": {"name": "feedback_type", "schema": "public", "values": ["bug_report", "feature_request", "improvement", "question", "complaint", "compliment", "other"]}, "public.app_permissions": {"name": "app_permissions", "schema": "public", "values": ["roles.manage", "billing.manage", "notifications.manage", "users.manage", "orders.manage", "feedback.manage", "analytics.view", "api_keys.manage", "content.manage", "system.manage"]}, "public.notification_channel": {"name": "notification_channel", "schema": "public", "values": ["in_app", "email"]}, "public.notification_type": {"name": "notification_type", "schema": "public", "values": ["info", "warning", "error", "success", "payment", "credit", "usage", "system", "security"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}