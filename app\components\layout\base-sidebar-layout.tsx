/**
 * Base Sidebar Layout Component
 * Unified layout for console and admin pages with configurable navigation
 */

import { Link, useLocation } from "@remix-run/react";
import { Home, Menu, X } from "lucide-react";
import { type ReactNode, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { cn } from "~/core/utils/cn";

export interface NavItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
  children?: NavItem[];
}

export interface UserInfo {
  name: string;
  email?: string;
  avatar?: string;
  plan?: string;
  credits?: number;
  initials?: string;
}

export interface SidebarConfig {
  title: string;
  logo?: ReactNode;
  navigation: NavItem[];
  user?: UserInfo;
  footerActions?: NavItem[];
  basePath: string; // e.g., "/console" or "/admin"
}

export interface BaseSidebarLayoutProps {
  children: ReactNode;
  sidebarConfig: SidebarConfig;
  className?: string;
  topBarActions?: ReactNode;
}

function NavLink({ item, isActive }: { item: NavItem; isActive: boolean }) {
  return (
    <Link
      to={item.url}
      className={cn(
        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
        isActive
          ? "bg-primary text-primary-foreground"
          : "text-muted-foreground hover:text-foreground hover:bg-muted"
      )}
    >
      {item.icon}
      <span className="flex-1">{item.title}</span>
      {item.badge && (
        <Badge variant={isActive ? "secondary" : "outline"} className="text-xs">
          {item.badge}
        </Badge>
      )}
    </Link>
  );
}

function UserSection({ user }: { user?: UserInfo }) {
  if (!user) return null;

  return (
    <div className="p-4 border-b">
      <div className="flex items-center gap-3">
        <Avatar>
          {user.avatar && <AvatarImage src={user.avatar} />}
          <AvatarFallback>{user.initials || user.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{user.name}</p>
          {user.email && <p className="text-xs text-muted-foreground truncate">{user.email}</p>}
          <div className="flex items-center gap-2 mt-1">
            {user.plan && (
              <Badge variant="secondary" className="text-xs">
                {user.plan}
              </Badge>
            )}
            {user.credits !== undefined && (
              <span className="text-xs text-muted-foreground">
                {user.credits.toLocaleString()} credits
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BaseSidebarLayout({
  children,
  sidebarConfig,
  className = "",
  topBarActions,
}: BaseSidebarLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === sidebarConfig.basePath) {
      return location.pathname === sidebarConfig.basePath;
    }
    return location.pathname.startsWith(url);
  };

  const getBreadcrumb = () => {
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const currentPage = pathSegments[pathSegments.length - 1];

    if (location.pathname === sidebarConfig.basePath) {
      return sidebarConfig.title;
    }

    return currentPage?.replace("-", " ") || sidebarConfig.title;
  };

  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
          onKeyDown={(e) => {
            if (e.key === "Escape") {
              setSidebarOpen(false);
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Close sidebar"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <Link to={sidebarConfig.basePath} className="flex items-center gap-2">
              {sidebarConfig.logo || (
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Home className="h-5 w-5 text-primary-foreground" />
                </div>
              )}
              <span className="font-bold text-lg">{sidebarConfig.title}</span>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* User Info */}
          <UserSection user={sidebarConfig.user} />

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {sidebarConfig.navigation.map((item) => (
              <NavLink key={item.url} item={item} isActive={isActive(item.url)} />
            ))}
          </nav>

          {/* Footer Actions */}
          {sidebarConfig.footerActions && sidebarConfig.footerActions.length > 0 && (
            <div className="p-4 border-t space-y-2">
              {sidebarConfig.footerActions.map((action) => (
                <Button
                  key={action.url}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start",
                    action.title.toLowerCase().includes("sign out") ||
                      action.title.toLowerCase().includes("logout")
                      ? "text-red-600 hover:text-red-600 hover:bg-red-50"
                      : ""
                  )}
                  asChild
                >
                  <Link to={action.url}>
                    {action.icon}
                    {action.title}
                  </Link>
                </Button>
              ))}
            </div>
          )}
        </div>
      </aside>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Breadcrumb */}
              <nav className="flex items-center gap-2 text-sm">
                <Link to="/" className="text-muted-foreground hover:text-foreground">
                  <Home className="h-4 w-4" />
                </Link>
                <span className="text-muted-foreground">/</span>
                <Link
                  to={sidebarConfig.basePath}
                  className="text-muted-foreground hover:text-foreground"
                >
                  {sidebarConfig.title}
                </Link>
                {location.pathname !== sidebarConfig.basePath && (
                  <>
                    <span className="text-muted-foreground">/</span>
                    <span className="font-medium capitalize">{getBreadcrumb()}</span>
                  </>
                )}
              </nav>
            </div>

            {/* Top bar actions */}
            {topBarActions && <div className="flex items-center gap-4">{topBarActions}</div>}
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}
