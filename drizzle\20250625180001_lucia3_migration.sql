-- Lucia 3 Migration: Update schema for Lucia 3 compatibility

-- 1. Update users table: rename googleSub to googleId and change id to text
ALTER TABLE "users" ALTER COLUMN "id" TYPE text;
ALTER TABLE "users" RENAME COLUMN "google_sub" TO "google_id";

-- 2. Drop and recreate sessions table with Lucia 3 schema
DROP TABLE IF EXISTS "sessions" CASCADE;
CREATE TABLE "sessions" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "expires_at" timestamp with time zone NOT NULL,
  CONSTRAINT "sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
);

-- 3. Create indexes for sessions table
CREATE INDEX "sessions_user_idx" ON "sessions" ("user_id");
CREATE INDEX "sessions_expires_idx" ON "sessions" ("expires_at");

-- 4. Drop the old key table (not used in Lucia 3)
DROP TABLE IF EXISTS "key" CASCADE;

-- 5. Update other tables to use text user_id instead of uuid
ALTER TABLE "accounts" ALTER COLUMN "user_id" TYPE text;
ALTER TABLE "accounts_memberships" ALTER COLUMN "user_id" TYPE text;
ALTER TABLE "notifications" ALTER COLUMN "user_id" TYPE text;
ALTER TABLE "api_keys" ALTER COLUMN "user_id" TYPE text;
ALTER TABLE "invitations" ALTER COLUMN "invited_by" TYPE text;
ALTER TABLE "invitations" ALTER COLUMN "accepted_by" TYPE text;

-- 6. Update uuid references in users table
ALTER TABLE "users" ALTER COLUMN "invited_by" TYPE text;

-- 7. Update google_id index
DROP INDEX IF EXISTS "users_google_sub_idx";
CREATE INDEX "users_google_id_idx" ON "users" ("google_id");