// Re-export utility modules for easier imports

// Browser cache utilities (moved from app/lib/ui)
export * from "./cache";
// Formatting utilities
export * from "./formatting";

// API and response utilities (moved from app/lib/api)
export * from "./response";
// Core utilities
export * from "./time";

// Toast notification utilities (moved from app/lib/ui)
export * from "./toast";
export * from "./cn";
// Validation utilities
export * from "./validation";

// Common utility types
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export interface SortParams {
  sortBy: string;
  sortOrder: "asc" | "desc";
}

export interface FilterParams {
  [key: string]: string | number | boolean | null | undefined;
}

export interface QueryParams extends PaginationParams, SortParams, FilterParams {}

// Utility functions
export function createPagination(page: number, limit: number): PaginationParams {
  const offset = (page - 1) * limit;
  return { page, limit, offset };
}

export function parseQueryParams(searchParams: URLSearchParams): Partial<QueryParams> {
  const params: Partial<QueryParams> = {};

  // Parse pagination
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");
  if (!isNaN(page)) params.page = page;
  if (!isNaN(limit)) params.limit = limit;
  if (params.page && params.limit) {
    params.offset = (params.page - 1) * params.limit;
  }

  // Parse sorting
  const sortBy = searchParams.get("sortBy");
  const sortOrder = searchParams.get("sortOrder") as "asc" | "desc";
  if (sortBy) params.sortBy = sortBy;
  if (sortOrder && ["asc", "desc"].includes(sortOrder)) {
    params.sortOrder = sortOrder;
  }

  // Parse other filters
  for (const [key, value] of searchParams.entries()) {
    if (!["page", "limit", "sortBy", "sortOrder"].includes(key)) {
      params[key] = value;
    }
  }

  return params;
}

export function buildQueryString(params: Partial<QueryParams>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== "") {
      searchParams.set(key, String(value));
    }
  });

  return searchParams.toString();
}
