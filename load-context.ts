import { createDb, type Database } from "./app/core/db";

let dbInstance: Database | null = null;

declare module "@remix-run/node" {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface AppLoadContext extends ReturnType<typeof getLoadContext> {
    // This will merge the result of `getLoadContext` into the `AppLoadContext`
  }
}

export function getLoadContext() {
  // Initialize database
  if (!dbInstance) {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      // In development, provide a warning instead of throwing an error
      console.warn(
        "⚠️  DATABASE_URL environment variable is not set. Database functionality will be disabled."
      );
      console.warn(
        "   To enable database features, add DATABASE_URL to your .env file or environment variables."
      );
      dbInstance = null;
    } else {
      dbInstance = createDb(databaseUrl);
    }
  }

  return {
    db: dbInstance,
  };
}
