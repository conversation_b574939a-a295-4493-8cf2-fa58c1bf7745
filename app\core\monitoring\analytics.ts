/**
 * Enhanced Google Analytics implementation for Remix with Cloudflare
 */

type GtagCommand = "config" | "event" | "js" | "set" | "consent";
type GtagParams = Record<string, string | number | boolean | string[]>;
type DataLayerItem = [GtagCommand, string, GtagParams?] | [GtagCommand, Date] | unknown[];

interface Metric {
  name: string;
  value: number;
  delta: number;
  id: string;
}

interface ConsentState {
  analytics_storage: "granted" | "denied";
  ad_storage: "granted" | "denied";
  ad_user_data: "granted" | "denied";
  ad_personalization: "granted" | "denied";
}

declare global {
  interface Window {
    gtag?: (command: GtagCommand, action: string, params?: GtagParams) => void;
    dataLayer?: DataLayerItem[];
    GA_TRACKING_ID?: string;
  }
}

// Analytics configuration - lazy initialization to avoid global scope issues
export const getAnalyticsConfig = () => {
  // Only track in production (detect by hostname)
  const enabled =
    typeof window !== "undefined" &&
    !window.location.hostname.includes("localhost") &&
    !window.location.hostname.includes("127.0.0.1");

  // Debug mode for development
  const debug =
    typeof window !== "undefined" &&
    (window.location.hostname.includes("localhost") ||
      window.location.hostname.includes("127.0.0.1"));

  return { enabled, debug };
};

const getGaTrackingId = (): string | undefined => {
  if (typeof window !== "undefined" && window.GA_TRACKING_ID) {
    return window.GA_TRACKING_ID;
  }
  return undefined;
};

let isGAInitialized = false;

/**
 * Initialize Google Analytics with enhanced configuration
 */
export const initGA = () => {
  const gaId = getGaTrackingId();
  const analyticsConfig = getAnalyticsConfig();
  if (!gaId || !analyticsConfig.enabled) {
    if (analyticsConfig.debug) {
      console.log("GA not initialized: missing ID or not in production");
    }
    return;
  }

  if (typeof window !== "undefined") {
    window.dataLayer = window.dataLayer || [];
    if (typeof window.gtag !== "function") {
      window.gtag = function (...args: [GtagCommand, string, GtagParams?]) {
        window.dataLayer?.push(args);
      };
    }

    // Enhanced initialization with performance tracking
    window.gtag("js", new Date());
    window.gtag("config", gaId, {
      send_page_view: false, // We handle page views manually
      custom_map: {
        custom_parameter_1: "user_type",
        custom_parameter_2: "page_category",
      },
    });

    isGAInitialized = true;

    if (analyticsConfig.debug) {
      console.log("GA initialized with ID:", gaId);
    }
  }
};

/**
 * Get page category from URL
 */
const getPageCategory = (url: string): string => {
  if (url.includes("/admin")) return "admin";
  if (url.includes("/blog")) return "blog";
  if (url.includes("/auth")) return "auth";
  if (url.includes("/commerce")) return "commerce";
  if (url.includes("/dev")) return "dev";
  if (url.includes("/console")) return "console";
  if (url.includes("/legal")) return "legal";
  if (url === "/") return "home";
  return "other";
};

/**
 * Enhanced page view tracking with performance metrics
 */
export const pageview = (url: string, title?: string) => {
  const gaId = getGaTrackingId();
  const analyticsConfig = getAnalyticsConfig();
  if (!gaId || !analyticsConfig.enabled) {
    if (analyticsConfig.debug) {
      console.log("Pageview not tracked:", url);
    }
    return;
  }

  if (typeof window !== "undefined" && window.gtag) {
    // Track page view
    window.gtag("config", gaId, {
      page_path: url,
      page_title: title,
      custom_parameter_2: getPageCategory(url),
    });

    // Track Core Web Vitals
    trackWebVitals();

    if (analyticsConfig.debug) {
      console.log("Pageview tracked:", url, title);
    }
  }
};

/**
 * Enhanced event tracking
 */
interface EventParams {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, string | number | boolean>;
  nonInteraction?: boolean;
}

export const event = ({
  action,
  category,
  label,
  value,
  custom_parameters,
  nonInteraction = false,
}: EventParams) => {
  const analyticsConfig = getAnalyticsConfig();
  if (!isGAInitialized || !window.gtag) {
    if (analyticsConfig.debug) {
      console.log("GA: Event not tracked (not initialized)", { category, action, label, value });
    }
    return;
  }

  const eventParams: GtagParams = {
    event_category: category,
    event_label: label,
    value: value,
    non_interaction: nonInteraction,
    ...custom_parameters,
  };

  window.gtag("event", action, eventParams);

  if (analyticsConfig.debug) {
    console.log("GA: Event tracked", { action, category, label, value, custom_parameters });
  }
};

/**
 * Track Core Web Vitals
 */
const trackWebVitals = () => {
  if (typeof window === "undefined" || !window.gtag) return;

  const analyticsConfig = getAnalyticsConfig();

  // Track FCP (First Contentful Paint)
  if ("PerformanceObserver" in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === "first-contentful-paint") {
            event({
              action: "web_vital",
              category: "Performance",
              label: "FCP",
              value: Math.round(entry.startTime),
            });
          }
        }
      });
      observer.observe({ entryTypes: ["paint"] });
    } catch (error) {
      if (analyticsConfig.debug) {
        console.warn("Failed to track FCP:", error);
      }
    }

    // Track LCP (Largest Contentful Paint)
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        event({
          action: "web_vital",
          category: "Performance",
          label: "LCP",
          value: Math.round(lastEntry.startTime),
        });
      });
      observer.observe({ entryTypes: ["largest-contentful-paint"] });
    } catch (error) {
      if (analyticsConfig.debug) {
        console.warn("Failed to track LCP:", error);
      }
    }

    // Track CLS (Cumulative Layout Shift)
    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "layout-shift" && !(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
      });
      observer.observe({ entryTypes: ["layout-shift"] });

      // Report CLS when the page is about to be unloaded
      window.addEventListener("beforeunload", () => {
        event({
          action: "web_vital",
          category: "Performance",
          label: "CLS",
          value: Math.round(clsValue * 1000),
        });
      });
    } catch (error) {
      if (analyticsConfig.debug) {
        console.warn("Failed to track CLS:", error);
      }
    }
  }
};

/**
 * Convenience functions for common tracking scenarios
 */

// Track user interactions
export const trackClick = (element: string, location?: string) => {
  event({
    action: "click",
    category: "User Interaction",
    label: element,
    custom_parameters: location ? { page_location: location } : undefined,
  });
};

// Track form submissions
export const trackFormSubmit = (formName: string, success = true) => {
  event({
    action: "form_submit",
    category: "Form",
    label: formName,
    custom_parameters: { success: success.toString() },
  });
};

// Track errors
export const trackError = (description: string, fatal = false) => {
  event({
    action: "error",
    category: "Error",
    label: description,
    custom_parameters: {
      fatal: fatal.toString(),
      page: typeof window !== "undefined" ? window.location.pathname : "unknown",
    },
  });
};

/**
 * GDPR Consent Management
 */
export const updateConsent = (granted: boolean) => {
  if (typeof window !== "undefined" && window.gtag) {
    const analyticsConfig = getAnalyticsConfig();
    window.gtag("consent", "update", {
      analytics_storage: granted ? "granted" : "denied",
      ad_storage: granted ? "granted" : "denied",
      ad_user_data: granted ? "granted" : "denied",
      ad_personalization: granted ? "granted" : "denied",
    });

    if (analyticsConfig.debug) {
      console.log("Consent updated:", granted ? "granted" : "denied");
    }
  }
};

// Track scroll depth
export const trackScrollDepth = (depth: number) => {
  event({
    action: "scroll_depth",
    category: "Performance",
    label: "Scroll Depth",
    value: depth,
  });
};

// Track search queries
export const trackSearch = (query: string, category?: string, resultsCount?: number) => {
  event({
    action: "search",
    category: "Search",
    label: query,
    value: resultsCount,
    custom_parameters: category ? { search_category: category } : undefined,
  });
};

// Track login/logout events
export const trackLogin = (method: string) => {
  event({
    action: "login",
    category: "User Interaction",
    label: method,
  });
};

export const trackLogout = () => {
  event({
    action: "logout",
    category: "User Interaction",
  });
};

// Track theme changes
export const trackThemeChange = (theme: string) => {
  event({
    action: "theme_change",
    category: "User Interaction",
    label: theme,
  });
};

// Track custom dimensions/metrics
export const trackDimension = (index: number, value: string) => {
  event({
    action: "custom_dimension",
    category: "User Interaction",
    label: `custom_dimension_${index}`,
    custom_parameters: { dimension_value: value },
  });
};

// Track user ID
export const setUserID = (userID: string) => {
  if (typeof window !== "undefined" && window.gtag) {
    const analyticsConfig = getAnalyticsConfig();
    window.gtag("set", "user_id", { user_id: userID });

    if (analyticsConfig.debug) {
      console.log("User ID set:", userID);
    }
  }
};

// Track user properties
export const setUserProperty = (name: string, value: string) => {
  if (typeof window !== "undefined" && window.gtag) {
    const analyticsConfig = getAnalyticsConfig();
    window.gtag("set", "user_properties", { [name]: value });

    if (analyticsConfig.debug) {
      console.log("User property set:", name, value);
    }
  }
};

// Track consent status
export const setConsent = (consent: ConsentState) => {
  if (typeof window !== "undefined" && window.gtag) {
    const analyticsConfig = getAnalyticsConfig();
    window.gtag("consent", "update", consent);

    if (analyticsConfig.debug) {
      console.log("Consent set:", consent);
    }
  }
};

// Track e-commerce events
export const trackPurchase = (
  transactionID: string,
  value: number,
  currency: string,
  items: any[]
) => {
  event({
    action: "purchase",
    category: "eCommerce",
    label: transactionID,
    value: value,
    custom_parameters: {
      currency: currency,
      transaction_id: transactionID,
      items: JSON.stringify(items),
    },
  });
};

// Track form submissions
export const trackFormSubmission = (formId: string, formName?: string) => {
  event({
    action: "form_submission",
    category: "Form",
    label: formId,
    custom_parameters: formName ? { form_name: formName } : undefined,
  });
};

// Track video engagement
export const trackVideo = (
  videoName: string,
  action: "play" | "pause" | "complete",
  progress?: number
) => {
  event({
    action: `video_${action}`,
    category: "Video",
    label: videoName,
    value: progress,
  });
};

// Track file downloads
export const trackFileDownload = (fileName: string, fileExtension: string, fileSize?: number) => {
  event({
    action: "file_download",
    category: "Download",
    label: fileName,
    custom_parameters: {
      file_extension: fileExtension,
      file_size: fileSize?.toString(),
    },
  });
};

// Track outbound link clicks
export const trackOutboundLink = (url: string) => {
  event({
    action: "outbound_link",
    category: "Navigation",
    label: url,
  });
};

// Track social interactions
export const trackSocial = (network: string, action: string, target: string) => {
  event({
    action: "social_interaction",
    category: "Social",
    label: `${network}_${action}`,
    custom_parameters: {
      social_network: network,
      social_action: action,
      social_target: target,
    },
  });
};
