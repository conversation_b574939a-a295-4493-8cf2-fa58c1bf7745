/**
 * State Components
 * Reusable components for loading, error, and empty states
 */

import { Link } from "@remix-run/react";
import { AlertCircle, FileX, Loader2, RefreshCw, Search, Wifi, WifiOff } from "lucide-react";
import type { ReactNode } from "react";
import { backgrounds, layouts, spacing, text } from "~/core/styles/common";
import { cn } from "~/core/utils/cn";
import { Button } from "./button";
import { Card, CardContent } from "./card";

// Base state component props
interface BaseStateProps {
  title?: string;
  message?: string;
  icon?: ReactNode;
  action?: {
    label: string;
    onClick?: () => void;
    href?: string;
  };
  className?: string;
  fullHeight?: boolean;
}

// Base state component
function BaseState({
  title,
  message,
  icon,
  action,
  className,
  fullHeight = false,
}: BaseStateProps) {
  const containerClasses = cn(
    layouts.flexColCenter,
    spacing.sectionLg,
    text.bodyMuted,
    fullHeight ? "min-h-screen" : "py-12",
    className
  );

  return (
    <div className={containerClasses}>
      <div className="text-center max-w-md">
        {icon && <div className="mb-4 flex justify-center">{icon}</div>}

        {title && <h3 className={cn(text.heading3, "mb-2")}>{title}</h3>}

        {message && <p className={cn(text.bodyMuted, "mb-6 leading-6")}>{message}</p>}

        {action && (
          <div className="flex justify-center">
            {action.href ? (
              <Button asChild>
                <Link to={action.href}>{action.label}</Link>
              </Button>
            ) : (
              <Button onClick={action.onClick}>{action.label}</Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Loading State Component
export interface LoadingStateProps extends Omit<BaseStateProps, "icon"> {
  size?: "sm" | "md" | "lg";
  variant?: "spinner" | "skeleton" | "dots";
}

export function LoadingState({
  title = "Loading...",
  message,
  size = "md",
  variant = "spinner",
  className,
  fullHeight = false,
  ...props
}: LoadingStateProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  const icons = {
    spinner: (
      <Loader2 className={cn(sizeClasses[size], "animate-spin text-muted-foreground opacity-40")} />
    ),
    dots: (
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="h-2 w-2 bg-muted-foreground opacity-40 rounded-full animate-pulse"
            style={{ animationDelay: `${i * 0.15}s` }}
          />
        ))}
      </div>
    ),
    skeleton: (
      <div className="space-y-3">
        <div className="h-4 bg-muted rounded w-3/4 animate-pulse" />
        <div className="h-4 bg-muted rounded w-1/2 animate-pulse" />
      </div>
    ),
  };

  return (
    <BaseState
      title={title}
      message={message}
      icon={icons[variant]}
      className={className}
      fullHeight={fullHeight}
      {...props}
    />
  );
}

// Error State Component
export interface ErrorStateProps extends Omit<BaseStateProps, "icon"> {
  error?: Error | string | null;
  showRetry?: boolean;
  onRetry?: () => void;
  variant?: "error" | "not-found" | "network";
}

export function ErrorState({
  title,
  message,
  error,
  showRetry = true,
  onRetry,
  variant = "error",
  className,
  fullHeight = false,
  action,
  ...props
}: ErrorStateProps) {
  const getVariantConfig = () => {
    switch (variant) {
      case "not-found":
        return {
          defaultTitle: "Page Not Found",
          defaultMessage: "The page you're looking for doesn't exist.",
          icon: <FileX className="h-12 w-12 text-muted-foreground opacity-40" />,
          action: action || { label: "Go Home", href: "/" },
        };
      case "network":
        return {
          defaultTitle: "Connection Error",
          defaultMessage: "Unable to connect to the server. Please check your internet connection.",
          icon: <WifiOff className="h-12 w-12 text-muted-foreground opacity-40" />,
          action: showRetry && onRetry ? { label: "Try Again", onClick: onRetry } : action,
        };
      default:
        return {
          defaultTitle: "Something went wrong",
          defaultMessage: error
            ? typeof error === "string"
              ? error
              : error.message
            : "An unexpected error occurred.",
          icon: <AlertCircle className="h-12 w-12 text-red-500 opacity-60" />,
          action: showRetry && onRetry ? { label: "Try Again", onClick: onRetry } : action,
        };
    }
  };

  const config = getVariantConfig();

  return (
    <BaseState
      title={title || config.defaultTitle}
      message={message || config.defaultMessage}
      icon={config.icon}
      action={config.action}
      className={className}
      fullHeight={fullHeight}
      {...props}
    />
  );
}

// Empty State Component
export interface EmptyStateProps extends Omit<BaseStateProps, "icon"> {
  variant?: "search" | "data" | "generic";
  searchTerm?: string;
}

export function EmptyState({
  title,
  message,
  variant = "generic",
  searchTerm,
  className,
  fullHeight = false,
  action,
  ...props
}: EmptyStateProps) {
  const getVariantConfig = () => {
    switch (variant) {
      case "search":
        return {
          defaultTitle: searchTerm ? `No results for "${searchTerm}"` : "No search results",
          defaultMessage: "Try adjusting your search terms or filters.",
          icon: <Search className="h-12 w-12 text-muted-foreground opacity-40" />,
        };
      case "data":
        return {
          defaultTitle: "No data available",
          defaultMessage: "There's nothing to display right now.",
          icon: <FileX className="h-12 w-12 text-muted-foreground opacity-40" />,
        };
      default:
        return {
          defaultTitle: "Nothing here yet",
          defaultMessage: "This section is empty.",
          icon: <FileX className="h-12 w-12 text-muted-foreground opacity-40" />,
        };
    }
  };

  const config = getVariantConfig();

  return (
    <BaseState
      title={title || config.defaultTitle}
      message={message || config.defaultMessage}
      icon={config.icon}
      action={action}
      className={className}
      fullHeight={fullHeight}
      {...props}
    />
  );
}

// Inline Loading Component (for smaller areas)
export function InlineLoading({
  size = "sm",
  className,
}: {
  size?: "sm" | "md";
  className?: string;
}) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
  };

  return <Loader2 className={cn(sizeClasses[size], "animate-spin", className)} />;
}

// Skeleton Loader Component
export interface SkeletonProps {
  className?: string;
  lines?: number;
  avatar?: boolean;
}

export function Skeleton({ className, lines = 3, avatar = false }: SkeletonProps) {
  return (
    <div className={cn("animate-pulse", className)}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-muted h-10 w-10" />
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded w-1/4" />
            <div className="h-3 bg-muted rounded w-1/3" />
          </div>
        </div>
      )}

      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn("h-4 bg-muted rounded", i === lines - 1 ? "w-2/3" : "w-full")}
          />
        ))}
      </div>
    </div>
  );
}

// Card with state
export function StateCard({
  children,
  loading = false,
  error = null,
  empty = false,
  className,
  onRetry,
}: {
  children: ReactNode;
  loading?: boolean;
  error?: Error | string | null;
  empty?: boolean;
  className?: string;
  onRetry?: () => void;
}) {
  return (
    <Card className={className}>
      <CardContent className="p-6">
        {loading && <LoadingState size="md" fullHeight={false} />}
        {error && !loading && (
          <ErrorState error={error} onRetry={onRetry} fullHeight={false} variant="error" />
        )}
        {empty && !loading && !error && <EmptyState variant="data" fullHeight={false} />}
        {!loading && !error && !empty && children}
      </CardContent>
    </Card>
  );
}

// Page-level error boundary fallback
export function PageErrorFallback({ error, onRetry }: { error: Error; onRetry?: () => void }) {
  return (
    <div className={cn(layouts.fullHeight, backgrounds.page)}>
      <ErrorState
        error={error}
        onRetry={onRetry}
        fullHeight={true}
        action={{
          label: "Go Home",
          href: "/",
        }}
      />
    </div>
  );
}
