import { render } from "@react-email/render"; // Assuming react-email might be used later for HTML templates
import { createResendClient, resend } from "./resend.server";
import type { EmailRecipient, EmailService, EmailTag, SendEmailParams } from "./types";

// Helper to format recipients for Resend
const formatRecipients = (recipients: EmailRecipient | EmailRecipient[]): string | string[] => {
  if (Array.isArray(recipients)) {
    return recipients.map((r) => (r.name ? `${r.name} <${r.email}>` : r.email));
  }
  return recipients.name ? `${recipients.name} <${recipients.email}>` : recipients.email;
};

// Helper to format a single recipient (e.g., for 'from' or 'replyTo')
const formatSingleRecipient = (recipient: EmailRecipient | string): string => {
  if (typeof recipient === "string") {
    return recipient;
  }
  return recipient.name ? `${recipient.name} <${recipient.email}>` : recipient.email;
};

// Create email service with environment context support
export function createEmailService(env?: Record<string, string | undefined>): EmailService {
  return {
    sendEmail: async (params: SendEmailParams) => {
      // Try to get Resend client with environment context first, fallback to default
      const resendClient = createResendClient(env) || resend;

      if (!resendClient) {
        console.error("Resend client is not initialized. Cannot send email.");
        return { success: false, error: "Email service is not configured." };
      }

      const { to, from, subject, text, html, replyTo, cc, bcc, tags, headers } = params;

      if (!html && !text) {
        console.error("Email must have either HTML or text content.");
        return { success: false, error: "Email content (HTML or text) is required." };
      }

      // Map our types to Resend's expected types
      const mailOptions: Parameters<typeof resend.emails.send>[0] = {
        to: formatRecipients(to),
        from: formatSingleRecipient(from), // This should match a verified domain in Resend
        subject,
        text,
        html,
      };

      if (replyTo) {
        mailOptions.reply_to = formatSingleRecipient(replyTo);
      }
      if (cc) {
        mailOptions.cc = formatRecipients(cc);
      }
      if (bcc) {
        mailOptions.bcc = formatRecipients(bcc);
      }
      if (tags && tags.length > 0) {
        mailOptions.tags = tags;
      }
      if (headers) {
        mailOptions.headers = headers;
      }

      try {
        console.log("Attempting to send email with options:", JSON.stringify(mailOptions, null, 2));
        const { data, error } = await resendClient.emails.send(mailOptions);

        if (error) {
          console.error("Error sending email via Resend:", error);
          // Consider more specific error handling based on Resend error codes if necessary
          return { success: false, error: error.message || "Failed to send email." };
        }

        console.log("Email sent successfully. Message ID:", data?.id);
        return { success: true, messageId: data?.id };
      } catch (e: any) {
        console.error("Caught exception in sendEmail:", e);
        // Ensure e.message is a string, default if not.
        const errorMessage =
          typeof e.message === "string"
            ? e.message
            : "An unexpected error occurred while sending email.";
        return { success: false, error: errorMessage };
      }
    },
  };
}

// For backward compatibility in development
export const emailService: EmailService = createEmailService();

// Example of how you might use react-email for HTML, if you install @react-email/render
// This is a placeholder and not fully integrated yet.
// const renderHtmlTemplate = (templateComponent: React.ReactElement): string => {
//   return render(templateComponent);
// };
