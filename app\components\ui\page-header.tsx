/**
 * Page Header Component
 * Reusable page header with title, subtitle, breadcrumbs, and actions
 */

import { Link } from "@remix-run/react";
import { ChevronRight } from "lucide-react";
import type { ReactNode } from "react";
import { cn } from "~/core/utils/cn";
import { Button } from "./button";

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface PageHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  actions?: ReactNode;
  breadcrumbs?: BreadcrumbItem[];
  className?: string;
  variant?: "default" | "centered" | "minimal";
}

export function PageHeader({
  title,
  subtitle,
  description,
  actions,
  breadcrumbs,
  className,
  variant = "default",
}: PageHeaderProps) {
  return (
    <div
      className={cn("bg-background border-b", variant === "centered" && "text-center", className)}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <nav className="flex items-center space-x-2 text-sm text-muted-foreground py-3 border-b">
            {breadcrumbs.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                {index > 0 && <ChevronRight className="h-4 w-4" />}
                {item.href ? (
                  <Link to={item.href} className="hover:text-foreground transition-colors">
                    {item.label}
                  </Link>
                ) : (
                  <span className="font-medium text-foreground">{item.label}</span>
                )}
              </div>
            ))}
          </nav>
        )}

        {/* Header Content */}
        <div
          className={cn(
            "py-6",
            variant === "minimal" && "py-4",
            breadcrumbs && breadcrumbs.length > 0 ? "pt-4" : ""
          )}
        >
          <div
            className={cn(
              "flex items-start justify-between",
              variant === "centered" && "flex-col items-center space-y-4"
            )}
          >
            <div className={cn("min-w-0 flex-1", variant === "centered" && "text-center")}>
              {/* Subtitle */}
              {subtitle && <p className="text-sm font-medium text-primary mb-1">{subtitle}</p>}

              {/* Title */}
              <h1
                className={cn(
                  "text-2xl font-bold tracking-tight text-foreground sm:text-3xl",
                  variant === "minimal" && "text-xl sm:text-2xl"
                )}
              >
                {title}
              </h1>

              {/* Description */}
              {description && (
                <p
                  className={cn(
                    "mt-2 text-sm text-muted-foreground sm:text-base",
                    variant === "centered" ? "max-w-2xl" : "max-w-4xl"
                  )}
                >
                  {description}
                </p>
              )}
            </div>

            {/* Actions */}
            {actions && (
              <div className="flex-shrink-0">
                <div className="flex space-x-3">{actions}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Preset configurations for common use cases
export function DashboardPageHeader({ title, actions }: { title: string; actions?: ReactNode }) {
  return (
    <PageHeader
      title={title}
      subtitle="Dashboard"
      breadcrumbs={[
        { label: "Home", href: "/" },
        { label: "Dashboard", href: "/console" },
        { label: title },
      ]}
      actions={actions}
    />
  );
}

export function AdminPageHeader({ title, actions }: { title: string; actions?: ReactNode }) {
  return (
    <PageHeader
      title={title}
      subtitle="Admin"
      breadcrumbs={[
        { label: "Home", href: "/" },
        { label: "Admin", href: "/admin" },
        { label: title },
      ]}
      actions={actions}
    />
  );
}

export function SettingsPageHeader({
  title,
  description,
  actions,
}: {
  title: string;
  description?: string;
  actions?: ReactNode;
}) {
  return (
    <PageHeader
      title={title}
      description={description}
      breadcrumbs={[
        { label: "Home", href: "/" },
        { label: "Settings", href: "/console/settings" },
        { label: title },
      ]}
      actions={actions}
      variant="minimal"
    />
  );
}

// Action button presets
export function HeaderActionButton({
  children,
  variant = "default",
  ...props
}: React.ComponentProps<typeof Button>) {
  return (
    <Button variant={variant} {...props}>
      {children}
    </Button>
  );
}

export function HeaderPrimaryAction({ children, ...props }: React.ComponentProps<typeof Button>) {
  return (
    <HeaderActionButton variant="default" {...props}>
      {children}
    </HeaderActionButton>
  );
}

export function HeaderSecondaryAction({ children, ...props }: React.ComponentProps<typeof Button>) {
  return (
    <HeaderActionButton variant="outline" {...props}>
      {children}
    </HeaderActionButton>
  );
}
