import { Bell, Settings } from "lucide-react";
import type { ReactNode } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { createAdminSidebarConfig } from "~/config/navigation";
import type { UserInfo } from "./base-sidebar-layout";
import BaseSidebarLayout from "./base-sidebar-layout";

export interface AdminLayoutProps {
  children: ReactNode;
  className?: string;
  user?: UserInfo;
}

export default function AdminLayout({ children, className = "", user }: AdminLayoutProps) {
  // Default admin user data (in a real app, this would come from props or context)
  const defaultUser: UserInfo = user || {
    name: "Admin User",
    email: "<EMAIL>",
    initials: "AU",
    plan: "Admin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  };

  const sidebarConfig = createAdminSidebarConfig(defaultUser);

  const topBarActions = (
    <>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="ghost" size="icon">
        <Settings className="h-5 w-5" />
      </Button>
    </>
  );

  return (
    <BaseSidebarLayout
      sidebarConfig={sidebarConfig}
      topBarActions={topBarActions}
      className={className}
    >
      {children}
    </BaseSidebarLayout>
  );
}
