import { <PERSON> } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>, Check, Menu } from "lucide-react";
import { type ReactNode, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "~/components/ui/sheet";
import { designSystem } from "~/config/design-system";
import { cn } from "~/core/utils/cn";
import Footer from "./footer";
import Header, { type HeaderProps } from "./header";
import Sidebar, { type SidebarProps } from "./sidebar";

// Hero section interface
interface HeroSection {
  badge?: {
    text: string;
    variant?: "default" | "success" | "warning" | "destructive";
  };
  title: string;
  subtitle?: string;
  description?: string;
  buttons?: {
    text: string;
    href: string;
    variant?: "primary" | "secondary" | "outline";
    icon?: ReactNode;
  }[];
  trustIndicators?: string[];
  backgroundPattern?: "gradient" | "dots" | "grid" | "none";
}

// Unified layout props
export interface UnifiedLayoutProps {
  children: ReactNode;

  // Layout options
  showHeader?: boolean;
  showFooter?: boolean;
  showSidebar?: boolean;
  className?: string;
  containerSize?: "default" | "small" | "full";

  // Header configuration
  headerProps?: HeaderProps;

  // Sidebar configuration
  sidebarProps?: Partial<SidebarProps>;
  sidebarCollapsible?: boolean;
  customSidebar?: ReactNode; // Custom sidebar component

  // Hero section
  hero?: HeroSection;
}

export default function UnifiedLayout({
  children,
  showHeader = true,
  showFooter = true,
  showSidebar = false,
  className = "",
  containerSize = "default",
  headerProps = {},
  sidebarProps = {},
  sidebarCollapsible = true,
  customSidebar,
  hero,
}: UnifiedLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const closeSidebar = () => setSidebarOpen(false);

  // Container class based on size
  const containerClass =
    containerSize === "small"
      ? designSystem.layouts.containerSmall
      : containerSize === "full"
        ? "w-full px-4"
        : designSystem.layouts.container;

  // Background pattern helper
  const getBackgroundPattern = (pattern?: string) => {
    switch (pattern) {
      case "gradient":
      case "dots":
      case "grid":
        return "bg-muted/30";
      default:
        return "bg-background";
    }
  };

  // Button variant helper
  const getButtonVariant = (variant?: string) => {
    switch (variant) {
      case "primary":
        return "default";
      case "secondary":
        return "secondary";
      case "outline":
        return "outline";
      default:
        return "default";
    }
  };

  return (
    <div className={cn("min-h-screen flex flex-col", className)}>
      {/* Header */}
      {showHeader && <Header {...headerProps} />}

      <div className="flex flex-1">
        {/* Desktop Sidebar */}
        {showSidebar && (
          <aside
            className={cn(
              "hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:z-40 lg:w-64 transition-all duration-300",
              showHeader ? "lg:top-16" : "lg:top-0",
              sidebarCollapsed ? "lg:w-16" : "lg:w-64"
            )}
          >
            {customSidebar || (
              <Sidebar {...sidebarProps} className={cn("h-full", sidebarProps.className)} />
            )}
          </aside>
        )}

        {/* Mobile Sidebar */}
        {showSidebar && (
          <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="fixed top-4 left-4 z-50 lg:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle sidebar</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0">
              {customSidebar || (
                <Sidebar
                  {...sidebarProps}
                  onClose={closeSidebar}
                  showCloseButton={true}
                  className="h-full"
                />
              )}
            </SheetContent>
          </Sheet>
        )}

        {/* Main Content Area */}
        <main
          className={cn(
            "flex-1 flex flex-col",
            showSidebar ? "lg:ml-64" : "",
            showHeader ? (showSidebar ? "lg:pt-0" : "pt-16") : ""
          )}
        >
          {/* Hero Section */}
          {hero && (
            <section
              className={`relative overflow-hidden ${getBackgroundPattern(hero.backgroundPattern)}`}
            >
              {/* Minimal decorative elements */}
              <div className="absolute inset-0 overflow-hidden opacity-50">
                <div className="absolute top-1/4 right-0 w-px h-24 bg-border" />
                <div className="absolute bottom-1/4 left-0 w-px h-24 bg-border" />
              </div>

              <div className={`relative z-10 ${containerClass} py-16 lg:py-20`}>
                <div className="text-center max-w-4xl mx-auto">
                  {/* Badge */}
                  {hero.badge && (
                    <div className="mb-8">
                      <Badge
                        variant={hero.badge.variant || "default"}
                        className="px-4 py-2 text-sm font-medium bg-muted border border-border"
                      >
                        {hero.badge.text}
                      </Badge>
                    </div>
                  )}

                  {/* Title */}
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
                    {hero.title}
                  </h1>

                  {/* Subtitle */}
                  {hero.subtitle && (
                    <h2 className="text-xl md:text-2xl font-semibold text-muted-foreground mb-4">
                      {hero.subtitle}
                    </h2>
                  )}

                  {/* Description */}
                  {hero.description && (
                    <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                      {hero.description}
                    </p>
                  )}

                  {/* Buttons */}
                  {hero.buttons && hero.buttons.length > 0 && (
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                      {hero.buttons.map((button, index) => (
                        <Button
                          key={`${button.text || button.label}-${index}`}
                          asChild
                          size="lg"
                          variant={index === 0 ? "default" : getButtonVariant(button.variant)}
                          className={`px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-200 ${
                            index === 0
                              ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                              : ""
                          }`}
                        >
                          <Link to={button.href} className="flex items-center gap-2">
                            {button.text}
                            {button.icon || (index === 0 && <ArrowRight className="h-5 w-5" />)}
                          </Link>
                        </Button>
                      ))}
                    </div>
                  )}

                  {/* Trust Indicators */}
                  {hero.trustIndicators && hero.trustIndicators.length > 0 && (
                    <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
                      {hero.trustIndicators.map((indicator, index) => (
                        <div
                          key={`${indicator.text || indicator.label}-${index}`}
                          className="flex items-center gap-2"
                        >
                          <Check className="h-4 w-4 text-success" />
                          <span>{indicator}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </section>
          )}

          {/* Page Content */}
          <div
            className={cn(
              "flex-1",
              showSidebar ? "p-4 lg:p-8" : "",
              hero ? "" : containerSize === "full" ? "" : containerClass
            )}
          >
            {hero ? (
              <div className={containerSize === "full" ? "" : containerClass}>{children}</div>
            ) : (
              children
            )}
          </div>

          {/* Footer */}
          {showFooter && <Footer />}
        </main>
      </div>
    </div>
  );
}
