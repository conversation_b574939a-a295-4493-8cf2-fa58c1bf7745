/**
 * Neon Database Optimization Utilities
 *
 * This module provides utilities for optimizing Neon database connections
 * and queries for serverless environments like Vercel.
 */

import { getVercelEnvironment, getVercelOptimizedConfig } from "../vercel/environment";

export interface NeonOptimizationConfig {
  // Connection settings
  connectionTimeout: number;
  idleTimeout: number;
  maxConnections: number;

  // Query settings
  queryTimeout: number;
  enableQueryLogging: boolean;
  enableSlowQueryLogging: boolean;
  slowQueryThreshold: number;

  // Cache settings
  enableQueryCache: boolean;
  queryCacheTtl: number;

  // Retry settings
  enableRetries: boolean;
  maxRetries: number;
  retryDelay: number;
}

/**
 * Get optimized Neon configuration based on environment
 */
export function getNeonOptimizedConfig(
  env?: Record<string, string | undefined>
): NeonOptimizationConfig {
  const vercelEnv = getVercelEnvironment(env);
  const optimizedConfig = getVercelOptimizedConfig(env);

  return {
    // Connection settings optimized for serverless
    connectionTimeout: optimizedConfig.database.connectionTimeoutMs,
    idleTimeout: optimizedConfig.database.idleTimeoutMs,
    maxConnections: optimizedConfig.database.maxConnections,

    // Query settings
    queryTimeout: vercelEnv.isVercel ? 20000 : 60000, // 20s for Vercel, 60s for others
    enableQueryLogging: optimizedConfig.database.enableLogging,
    enableSlowQueryLogging: vercelEnv.isProduction,
    slowQueryThreshold: 1000, // 1 second

    // Cache settings
    enableQueryCache: vercelEnv.isProduction,
    queryCacheTtl: optimizedConfig.cache.defaultTtl,

    // Retry settings
    enableRetries: true,
    maxRetries: vercelEnv.isVercel ? 2 : 3,
    retryDelay: 1000, // 1 second
  };
}

/**
 * Connection pool manager optimized for Neon + Vercel
 */
export class NeonConnectionManager {
  private static instance: NeonConnectionManager | null = null;
  private config: NeonOptimizationConfig;
  private connectionCount = 0;
  private lastActivity = Date.now();

  private constructor(config: NeonOptimizationConfig) {
    this.config = config;
  }

  static getInstance(env?: Record<string, string | undefined>): NeonConnectionManager {
    if (!NeonConnectionManager.instance) {
      const config = getNeonOptimizedConfig(env);
      NeonConnectionManager.instance = new NeonConnectionManager(config);
    }
    return NeonConnectionManager.instance;
  }

  /**
   * Check if we should create a new connection
   */
  shouldCreateConnection(): boolean {
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    const isIdle = timeSinceLastActivity > this.config.idleTimeout;
    const hasCapacity = this.connectionCount < this.config.maxConnections;

    return hasCapacity && !isIdle;
  }

  /**
   * Track connection usage
   */
  trackConnection(isNew = false) {
    if (isNew) {
      this.connectionCount++;
    }
    this.lastActivity = Date.now();
  }

  /**
   * Release connection
   */
  releaseConnection() {
    this.connectionCount = Math.max(0, this.connectionCount - 1);
  }

  /**
   * Get current connection stats
   */
  getStats() {
    return {
      activeConnections: this.connectionCount,
      maxConnections: this.config.maxConnections,
      lastActivity: new Date(this.lastActivity),
      timeSinceLastActivity: Date.now() - this.lastActivity,
    };
  }
}

/**
 * Query performance monitor for Neon
 */
export class NeonQueryMonitor {
  private static slowQueries: Array<{
    query: string;
    duration: number;
    timestamp: Date;
    params?: unknown[];
  }> = [];

  static startQuery(query: string, params?: unknown[]) {
    const startTime = Date.now();

    return {
      end: () => {
        const duration = Date.now() - startTime;
        const config = getNeonOptimizedConfig();

        // Log slow queries
        if (config.enableSlowQueryLogging && duration > config.slowQueryThreshold) {
          NeonQueryMonitor.logSlowQuery(query, duration, params);
        }

        // Log all queries in development
        if (config.enableQueryLogging) {
          console.log(`🔍 Query executed in ${duration}ms:`, query.substring(0, 100));
        }

        return duration;
      },
    };
  }

  private static logSlowQuery(query: string, duration: number, params?: unknown[]) {
    const slowQuery = {
      query: query.substring(0, 500), // Truncate long queries
      duration,
      timestamp: new Date(),
      params: params?.slice(0, 10), // Limit params for logging
    };

    NeonQueryMonitor.slowQueries.push(slowQuery);

    // Keep only last 100 slow queries
    if (NeonQueryMonitor.slowQueries.length > 100) {
      NeonQueryMonitor.slowQueries = NeonQueryMonitor.slowQueries.slice(-100);
    }

    console.warn(`🐌 Slow query detected (${duration}ms):`, slowQuery);
  }

  static getSlowQueries() {
    return [...NeonQueryMonitor.slowQueries];
  }

  static clearSlowQueries() {
    NeonQueryMonitor.slowQueries = [];
  }
}

/**
 * Neon-specific error handler
 */
export class NeonErrorHandler {
  static isRetryableError(error: unknown): boolean {
    if (!(error instanceof Error)) return false;

    const retryableErrors = [
      "connection timeout",
      "connection refused",
      "network error",
      "temporary failure",
      "rate limit",
      "too many connections",
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some((retryableError) => errorMessage.includes(retryableError));
  }

  static async withRetry<T>(
    operation: () => Promise<T>,
    config?: Partial<NeonOptimizationConfig>
  ): Promise<T> {
    const optimizedConfig = getNeonOptimizedConfig();
    const maxRetries = config?.maxRetries ?? optimizedConfig.maxRetries;
    const retryDelay = config?.retryDelay ?? optimizedConfig.retryDelay;

    let lastError: unknown;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries || !NeonErrorHandler.isRetryableError(error)) {
          throw error;
        }

        console.warn(`🔄 Retrying operation (attempt ${attempt + 1}/${maxRetries + 1}):`, error);

        // Exponential backoff
        const delay = retryDelay * 2 ** attempt;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}

/**
 * Health check utilities for Neon database
 */
export class NeonHealthCheck {
  static async checkConnection(db: any): Promise<{
    status: "healthy" | "unhealthy";
    latency: number;
    timestamp: Date;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // Simple query to test connection
      await db.execute("SELECT 1");

      const latency = Date.now() - startTime;

      return {
        status: "healthy",
        latency,
        timestamp: new Date(),
      };
    } catch (error) {
      const latency = Date.now() - startTime;

      return {
        status: "unhealthy",
        latency,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  static async checkPerformance(db: any): Promise<{
    connectionTime: number;
    queryTime: number;
    totalTime: number;
  }> {
    const startTime = Date.now();

    // Test connection time
    const connectionStart = Date.now();
    await db.execute("SELECT 1");
    const connectionTime = Date.now() - connectionStart;

    // Test query time
    const queryStart = Date.now();
    await db.execute("SELECT COUNT(*) FROM information_schema.tables");
    const queryTime = Date.now() - queryStart;

    const totalTime = Date.now() - startTime;

    return {
      connectionTime,
      queryTime,
      totalTime,
    };
  }
}
