import { Resend } from "resend";
import { createResendConfig, resendApiKey } from "./config";

// Create a function to get Resend client with environment context
export function createResendClient(env?: Record<string, string | undefined>): Resend | null {
  const { resendApiKey: apiKey } = createResendConfig(env);

  if (!apiKey) {
    console.error("RESEND_API_KEY is not configured. Email functionality will be disabled.");
    return null;
  }

  try {
    return new Resend(apiKey);
  } catch (error) {
    console.error("Failed to initialize Resend client:", error);
    return null;
  }
}

// For backward compatibility in development (when process.env is available)
let resend: Resend | null = null;

if (resendApiKey) {
  try {
    resend = new Resend(resendApiKey);
  } catch (error) {
    console.error("Failed to initialize Resend client:", error);
    // resend will remain null
  }
} else {
  console.warn("Resend client not initialized because RESEND_API_KEY is missing.");
}

export { resend };
