-- Add Google authentication fields to users table
-- Migration: add_google_auth_fields
-- Date: 2025-06-23

-- Add new columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS uuid UUID DEFAULT gen_random_uuid() UNIQUE,
ADD COLUMN IF NOT EXISTS theme VARCHAR(20) DEFAULT 'system',
ADD COLUMN IF NOT EXISTS signin_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS signin_provider VARCHAR(50),
ADD COLUMN IF NOT EXISTS signin_openid VARCHAR(255),
ADD COLUMN IF NOT EXISTS signin_ip VARCHAR(45),
ADD COLUMN IF NOT EXISTS is_affiliate BOOLEAN DEFAULT false NOT NULL;

-- Add new columns to sessions table
ALTER TABLE sessions
ADD COLUMN IF NOT EXISTS session_token VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS refresh_token VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS refresh_expires_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true NOT NULL;

-- Create indexes for new fields
CREATE INDEX IF NOT EXISTS users_uuid_idx ON users(uuid);
CREATE INDEX IF NOT EXISTS users_signin_provider_idx ON users(signin_provider);
CREATE INDEX IF NOT EXISTS users_signin_openid_idx ON users(signin_openid);

CREATE INDEX IF NOT EXISTS sessions_token_idx ON sessions(session_token);
CREATE INDEX IF NOT EXISTS sessions_refresh_token_idx ON sessions(refresh_token);
CREATE INDEX IF NOT EXISTS sessions_active_idx ON sessions(is_active);

-- Update existing users to have UUIDs if they don't have them
UPDATE users SET uuid = gen_random_uuid() WHERE uuid IS NULL;

-- Update existing sessions to have tokens if they don't have them
UPDATE sessions 
SET 
  session_token = COALESCE(session_token, encode(gen_random_bytes(32), 'hex')),
  refresh_token = COALESCE(refresh_token, encode(gen_random_bytes(32), 'hex')),
  refresh_expires_at = COALESCE(refresh_expires_at, expires_at + INTERVAL '23 days'),
  is_active = COALESCE(is_active, true)
WHERE session_token IS NULL OR refresh_token IS NULL;

-- Make uuid column NOT NULL after populating it
ALTER TABLE users ALTER COLUMN uuid SET NOT NULL;

-- Make session tokens NOT NULL after populating them
ALTER TABLE sessions ALTER COLUMN session_token SET NOT NULL;
ALTER TABLE sessions ALTER COLUMN refresh_token SET NOT NULL;
ALTER TABLE sessions ALTER COLUMN refresh_expires_at SET NOT NULL;
