/**
 * Stack Auth (Neon Auth) Component for Remix
 * Provides Stack Auth UI components and integration
 */

import { useEffect, useState } from "react";

interface StackAuthProps {
  projectId?: string;
  publishableKey?: string;
  enabled?: boolean;
  redirectUrl?: string;
}

// Stack Auth sign-in button component
export function StackAuthSignIn({ projectId, enabled = true, redirectUrl }: StackAuthProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleStackSignIn = () => {
    if (!projectId || !enabled) {
      console.error("Stack Auth: Project ID not configured or disabled");
      return;
    }

    setIsLoading(true);

    // Construct Stack Auth sign-in URL
    const baseUrl = `https://app.stack-auth.com/handler/${projectId}/signin`;
    const finalRedirectUrl = redirectUrl || window.location.origin + "/console";
    const signInUrl = `${baseUrl}?redirect_url=${encodeURIComponent(finalRedirectUrl)}`;

    console.log("Stack Auth: Redirecting to:", signInUrl);

    // Redirect to Stack Auth
    window.location.href = signInUrl;
  };

  if (!enabled || !projectId) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          Stack Auth is not configured. Please check your environment variables.
        </p>
      </div>
    );
  }

  return (
    <button
      type="button"
      onClick={handleStackSignIn}
      disabled={isLoading}
      className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-blue-300 rounded-lg shadow-sm bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoading ? (
        <>
          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          Redirecting...
        </>
      ) : (
        <>
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="m9 12 2 2 4-4"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Continue with Stack Auth
        </>
      )}
    </button>
  );
}

// Stack Auth sign-up button component
export function StackAuthSignUp({ projectId, enabled = true, redirectUrl }: StackAuthProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleStackSignUp = () => {
    if (!projectId || !enabled) {
      console.error("Stack Auth: Project ID not configured or disabled");
      return;
    }

    setIsLoading(true);

    // Construct Stack Auth sign-up URL
    const baseUrl = `https://app.stack-auth.com/handler/${projectId}/signup`;
    const finalRedirectUrl = redirectUrl || window.location.origin + "/console";
    const signUpUrl = `${baseUrl}?redirect_url=${encodeURIComponent(finalRedirectUrl)}`;

    console.log("Stack Auth: Redirecting to:", signUpUrl);

    // Redirect to Stack Auth
    window.location.href = signUpUrl;
  };

  if (!enabled || !projectId) {
    return null;
  }

  return (
    <button
      type="button"
      onClick={handleStackSignUp}
      disabled={isLoading}
      className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-green-300 rounded-lg shadow-sm bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoading ? (
        <>
          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          Redirecting...
        </>
      ) : (
        <>
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2" />
            <line x1="19" x2="19" y1="8" y2="14" stroke="currentColor" strokeWidth="2" />
            <line x1="22" x2="16" y1="11" y2="11" stroke="currentColor" strokeWidth="2" />
          </svg>
          Sign up with Stack Auth
        </>
      )}
    </button>
  );
}

// Combined Stack Auth component with both sign-in and sign-up options
export function StackAuthButtons({ projectId, enabled = true, redirectUrl }: StackAuthProps) {
  if (!enabled || !projectId) {
    return (
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800">
          Stack Auth (Neon Auth) is available but not fully configured.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <StackAuthSignIn projectId={projectId} enabled={enabled} redirectUrl={redirectUrl} />
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">New to Stack Auth?</span>
        </div>
      </div>
      <StackAuthSignUp projectId={projectId} enabled={enabled} redirectUrl={redirectUrl} />
    </div>
  );
}
