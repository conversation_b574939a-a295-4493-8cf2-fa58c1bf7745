// Toast notification utilities (moved from app/lib/ui/toast.ts)
import { toast } from "sonner";

/**
 * Toast notification options
 */
export interface ToastOptions {
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Enhanced toast notification utilities using Sonner
 */
export class ToastManager {
  /**
   * Display a success toast
   */
  static success(title: string, options?: ToastOptions) {
    if (options?.description) {
      toast.success(title, {
        description: options.description,
        duration: options.duration,
        action: options.action,
      });
    } else {
      toast.success(title, {
        duration: options?.duration,
        action: options?.action,
      });
    }
  }

  /**
   * Display an error toast
   */
  static error(title: string, options?: ToastOptions) {
    if (options?.description) {
      toast.error(title, {
        description: options.description,
        duration: options.duration,
        action: options.action,
      });
    } else {
      toast.error(title, {
        duration: options?.duration,
        action: options?.action,
      });
    }
  }

  /**
   * Display a warning toast
   */
  static warning(title: string, options?: ToastOptions) {
    if (options?.description) {
      toast.warning(title, {
        description: options.description,
        duration: options.duration,
        action: options.action,
      });
    } else {
      toast.warning(title, {
        duration: options?.duration,
        action: options?.action,
      });
    }
  }

  /**
   * Display an info toast
   */
  static info(title: string, options?: ToastOptions) {
    if (options?.description) {
      toast.info(title, {
        description: options.description,
        duration: options.duration,
        action: options.action,
      });
    } else {
      toast.info(title, {
        duration: options?.duration,
        action: options?.action,
      });
    }
  }

  /**
   * Display a loading toast
   */
  static loading(title: string, options?: Omit<ToastOptions, "action">) {
    return toast.loading(title, {
      description: options?.description,
      duration: options?.duration,
    });
  }

  /**
   * Display a custom toast
   */
  static custom(
    title: string,
    options?: ToastOptions & { type?: "default" | "success" | "error" | "warning" | "info" }
  ) {
    const toastFn = options?.type ? toast[options.type] : toast;

    if (options?.description) {
      toastFn(title, {
        description: options.description,
        duration: options.duration,
        action: options.action,
      });
    } else {
      toastFn(title, {
        duration: options?.duration,
        action: options?.action,
      });
    }
  }

  /**
   * Dismiss a specific toast
   */
  static dismiss(toastId?: string | number) {
    toast.dismiss(toastId);
  }

  /**
   * Dismiss all toasts
   */
  static dismissAll() {
    toast.dismiss();
  }
}

/**
 * Hook-style API for React components (backward compatibility)
 */
export function useNotify() {
  return {
    success: (title: string, message?: string) => {
      ToastManager.success(title, message ? { description: message } : undefined);
    },
    error: (title: string, message?: string) => {
      ToastManager.error(title, message ? { description: message } : undefined);
    },
    warning: (title: string, message?: string) => {
      ToastManager.warning(title, message ? { description: message } : undefined);
    },
    info: (title: string, message?: string) => {
      ToastManager.info(title, message ? { description: message } : undefined);
    },
    loading: (title: string, message?: string) => {
      return ToastManager.loading(title, message ? { description: message } : undefined);
    },
    dismiss: ToastManager.dismiss,
    dismissAll: ToastManager.dismissAll,
  };
}

// Static exports for direct usage
export const { success, error, warning, info, loading, dismiss, dismissAll } = ToastManager;
