CREATE TYPE IF NOT EXISTS "public"."app_permissions" AS ENUM('roles.manage', 'billing.manage', 'settings.manage', 'members.manage', 'invites.manage', 'api.manage', 'notifications.manage', 'users.manage', 'orders.manage', 'feedback.manage', 'analytics.view', 'api_keys.manage', 'content.manage', 'system.manage');
CREATE TYPE IF NOT EXISTS "public"."billing_provider" AS ENUM('stripe', 'lemon-squeezy', 'paddle');
CREATE TYPE IF NOT EXISTS "public"."notification_channel" AS ENUM('in_app', 'email', 'push', 'sms');
CREATE TYPE IF NOT EXISTS "public"."notification_type" AS ENUM('info', 'warning', 'error', 'success', 'payment', 'credit', 'usage', 'system', 'security');
CREATE TYPE IF NOT EXISTS "public"."payment_status" AS ENUM('pending', 'succeeded', 'failed');
CREATE TYPE IF NOT EXISTS "public"."subscription_status" AS ENUM('active', 'trialing', 'past_due', 'canceled', 'unpaid', 'incomplete', 'incomplete_expired', 'paused');
CREATE TYPE IF NOT EXISTS "public"."feedback_priority" AS ENUM('low', 'medium', 'high', 'urgent');
CREATE TYPE IF NOT EXISTS "public"."feedback_status" AS ENUM('open', 'in_progress', 'resolved', 'closed', 'duplicate');
CREATE TYPE IF NOT EXISTS "public"."feedback_type" AS ENUM('bug_report', 'feature_request', 'improvement', 'question', 'complaint', 'compliment', 'other');

CREATE TABLE IF NOT EXISTS "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"uuid" uuid DEFAULT gen_random_uuid() UNIQUE,
	"name" varchar(255),
	"email" varchar(255) NOT NULL UNIQUE,
	"avatar" text,
	"credits" integer DEFAULT 0 NOT NULL,
	"invite_code" varchar(10) UNIQUE,
	"invited_by" uuid,
	"signin_type" varchar(50),
	"signin_provider" varchar(50),
	"signin_openid" varchar(255),
	"signin_ip" varchar(45),
	"is_affiliate" boolean DEFAULT false NOT NULL,
	"email_verified" boolean DEFAULT false NOT NULL,
	"onboarding_completed" boolean DEFAULT false NOT NULL,
	"onboarding_step" varchar(50),
	"preferences" text,
	"theme" varchar(20) DEFAULT 'system',
	"google_sub" varchar(255) UNIQUE,
	"password_hash" text,
	"last_login_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "accounts" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" varchar(255) NOT NULL,
	"provider_user_id" varchar(255) NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"expires_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL UNIQUE,
	"description" text
);

CREATE TABLE IF NOT EXISTS "role_permissions" (
	"role_id" integer NOT NULL,
	"permission" "public"."app_permissions" NOT NULL,
	CONSTRAINT "role_permissions_role_id_permission_pk" PRIMARY KEY("role_id","permission")
);

CREATE TABLE IF NOT EXISTS "accounts_memberships" (
	"id" serial PRIMARY KEY NOT NULL,
	"account_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"role_id" integer NOT NULL,
	"invited_by" uuid,
	"invited_at" timestamp,
	"joined_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"expires_at" timestamp with time zone NOT NULL
);

CREATE TABLE IF NOT EXISTS "key" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"hashed_password" text
);
