// Common validation utilities

/**
 * Email validation regex pattern
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * URL validation regex pattern
 */
const URL_REGEX =
  /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;

/**
 * UUID validation regex pattern
 */
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validation utilities class
 */
export class Validator {
  /**
   * Validate email address
   */
  static email(email: string): boolean {
    return EMAIL_REGEX.test(email);
  }

  /**
   * Validate URL
   */
  static url(url: string): boolean {
    return URL_REGEX.test(url);
  }

  /**
   * Validate UUID
   */
  static uuid(uuid: string): boolean {
    return UUID_REGEX.test(uuid);
  }

  /**
   * Validate required field
   */
  static required(value: any): boolean {
    if (typeof value === "string") {
      return value.trim().length > 0;
    }
    return value != null && value !== undefined;
  }

  /**
   * Validate minimum length
   */
  static minLength(value: string, min: number): boolean {
    return value.length >= min;
  }

  /**
   * Validate maximum length
   */
  static maxLength(value: string, max: number): boolean {
    return value.length <= max;
  }

  /**
   * Validate number range
   */
  static range(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * Validate password strength
   * At least 8 characters, 1 uppercase, 1 lowercase, 1 number
   */
  static password(password: string): boolean {
    const hasMinLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);

    return hasMinLength && hasUppercase && hasLowercase && hasNumber;
  }

  /**
   * Validate phone number (basic US format)
   */
  static phone(phone: string): boolean {
    const cleaned = phone.replace(/\D/g, "");
    return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === "1");
  }

  /**
   * Validate credit card number (basic Luhn algorithm)
   */
  static creditCard(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\D/g, "");

    if (cleaned.length < 13 || cleaned.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  /**
   * Validate JSON string
   */
  static json(jsonString: string): boolean {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Form validation helper
 */
export class FormValidator {
  private errors: ValidationError[] = [];

  /**
   * Add validation error
   */
  addError(field: string, message: string, code?: string): this {
    this.errors.push({ field, message, code });
    return this;
  }

  /**
   * Validate field with custom function
   */
  field(name: string, value: any, validator: (value: any) => boolean, message: string): this {
    if (!validator(value)) {
      this.addError(name, message);
    }
    return this;
  }

  /**
   * Validate required field
   */
  required(name: string, value: any, message = `${name} is required`): this {
    if (!Validator.required(value)) {
      this.addError(name, message, "REQUIRED");
    }
    return this;
  }

  /**
   * Validate email field
   */
  email(name: string, value: string, message = "Invalid email address"): this {
    if (value && !Validator.email(value)) {
      this.addError(name, message, "INVALID_EMAIL");
    }
    return this;
  }

  /**
   * Validate password field
   */
  password(
    name: string,
    value: string,
    message = "Password must be at least 8 characters with uppercase, lowercase, and number"
  ): this {
    if (value && !Validator.password(value)) {
      this.addError(name, message, "WEAK_PASSWORD");
    }
    return this;
  }

  /**
   * Validate string length
   */
  length(name: string, value: string, min: number, max: number, message?: string): this {
    if (value) {
      if (!Validator.minLength(value, min)) {
        this.addError(name, message || `${name} must be at least ${min} characters`, "TOO_SHORT");
      }
      if (!Validator.maxLength(value, max)) {
        this.addError(name, message || `${name} must be at most ${max} characters`, "TOO_LONG");
      }
    }
    return this;
  }

  /**
   * Get validation result
   */
  getResult(): ValidationResult {
    return {
      isValid: this.errors.length === 0,
      errors: this.errors,
    };
  }

  /**
   * Reset validation errors
   */
  reset(): this {
    this.errors = [];
    return this;
  }
}

/**
 * Quick validation functions
 */
export const validate = {
  email: Validator.email,
  url: Validator.url,
  uuid: Validator.uuid,
  required: Validator.required,
  minLength: Validator.minLength,
  maxLength: Validator.maxLength,
  range: Validator.range,
  password: Validator.password,
  phone: Validator.phone,
  creditCard: Validator.creditCard,
  json: Validator.json,
};
