// Chat conversation and message schemas
import { relations } from "drizzle-orm";
import {
  boolean,
  index,
  integer,
  jsonb,
  pgTable,
  uuid as pgUuid,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "./users";

// Conversations table
export const conversations = pgTable(
  "conversations",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    title: varchar("title", { length: 255 }).notNull(),
    model: varchar("model", { length: 100 }),
    provider: varchar("provider", { length: 50 }),
    isArchived: boolean("is_archived").default(false).notNull(),
    lastMessageAt: timestamp("last_message_at"),
    metadata: jsonb("metadata"), // Additional conversation data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("conversations_user_idx").on(table.userId),
    archivedIdx: index("conversations_archived_idx").on(table.isArchived),
    lastMessageIdx: index("conversations_last_message_idx").on(table.lastMessageAt),
    modelIdx: index("conversations_model_idx").on(table.model),
    providerIdx: index("conversations_provider_idx").on(table.provider),
  })
);

// Messages table
export const messages = pgTable(
  "messages",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    conversationId: pgUuid("conversation_id")
      .notNull()
      .references(() => conversations.id, { onDelete: "cascade" }),
    role: varchar("role", { length: 20 }).notNull(), // "user" | "assistant" | "system"
    content: text("content").notNull(),
    model: varchar("model", { length: 100 }),
    provider: varchar("provider", { length: 50 }),
    tokenCount: integer("token_count"),
    metadata: jsonb("metadata"), // Additional message data (images, files, etc.)
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    conversationIdx: index("messages_conversation_idx").on(table.conversationId),
    roleIdx: index("messages_role_idx").on(table.role),
    createdIdx: index("messages_created_idx").on(table.createdAt),
    modelIdx: index("messages_model_idx").on(table.model),
    providerIdx: index("messages_provider_idx").on(table.provider),
  })
);

// Generated images table (for AI image generation)
export const generatedImages = pgTable(
  "generated_images",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    messageId: pgUuid("message_id").references(() => messages.id, { onDelete: "set null" }),
    prompt: text("prompt").notNull(),
    model: varchar("model", { length: 100 }).notNull(),
    provider: varchar("provider", { length: 50 }).notNull(),
    imageUrl: text("image_url").notNull(),
    width: integer("width"),
    height: integer("height"),
    parameters: jsonb("parameters"), // Generation parameters (steps, guidance, etc.)
    cost: integer("cost"), // Cost in credits
    metadata: jsonb("metadata"), // Additional image data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("generated_images_user_idx").on(table.userId),
    messageIdx: index("generated_images_message_idx").on(table.messageId),
    modelIdx: index("generated_images_model_idx").on(table.model),
    providerIdx: index("generated_images_provider_idx").on(table.provider),
    createdIdx: index("generated_images_created_idx").on(table.createdAt),
  })
);

// Relations
export const conversationsRelations = relations(conversations, ({ one, many }) => ({
  user: one(users, {
    fields: [conversations.userId],
    references: [users.id],
  }),
  messages: many(messages),
}));

export const messagesRelations = relations(messages, ({ one, many }) => ({
  conversation: one(conversations, {
    fields: [messages.conversationId],
    references: [conversations.id],
  }),
  generatedImages: many(generatedImages),
}));

export const generatedImagesRelations = relations(generatedImages, ({ one }) => ({
  user: one(users, {
    fields: [generatedImages.userId],
    references: [users.id],
  }),
  message: one(messages, {
    fields: [generatedImages.messageId],
    references: [messages.id],
  }),
}));
