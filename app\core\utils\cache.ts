// Browser storage cache utilities (moved from app/lib/ui/cache.ts)

/**
 * Cache configuration interface
 */
export interface CacheConfig {
  prefix?: string;
  defaultTTL?: number; // seconds
  storage?: Storage; // localStorage or sessionStorage
}

/**
 * Default cache configuration
 */
const DEFAULT_CONFIG: Required<CacheConfig> = {
  prefix: "app_cache",
  defaultTTL: 3600, // 1 hour
  storage: typeof window !== "undefined" ? localStorage : ({} as Storage),
};

/**
 * Enhanced cache utility class
 */
export class CacheManager {
  private config: Required<CacheConfig>;

  constructor(config: CacheConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  private getKey(key: string): string {
    return `${this.config.prefix}_${key}`;
  }

  private getCurrentTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Get data from cache
   */
  get<T = string>(key: string): T | null {
    if (typeof window === "undefined") {
      return null; // SSR environment
    }

    try {
      const fullKey = this.getKey(key);
      const valueWithExpires = this.config.storage.getItem(fullKey);

      if (!valueWithExpires) {
        return null;
      }

      const separatorIndex = valueWithExpires.indexOf(":");
      if (separatorIndex === -1) {
        return null;
      }

      const expiresAt = Number(valueWithExpires.substring(0, separatorIndex));
      const value = valueWithExpires.substring(separatorIndex + 1);
      const currentTimestamp = this.getCurrentTimestamp();

      if (expiresAt !== -1 && expiresAt < currentTimestamp) {
        // Value has expired
        this.remove(key);
        return null;
      }

      // Try to parse as JSON, fall back to string
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as T;
      }
    } catch (error) {
      console.warn("Cache get error:", error);
      return null;
    }
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, value: T, ttlSeconds?: number): void {
    if (typeof window === "undefined") {
      return; // SSR environment
    }

    try {
      const fullKey = this.getKey(key);
      const ttl = ttlSeconds ?? this.config.defaultTTL;
      const expiresAt = ttl === -1 ? -1 : this.getCurrentTimestamp() + ttl;

      const serializedValue = typeof value === "string" ? value : JSON.stringify(value);
      const valueWithExpires = `${expiresAt}:${serializedValue}`;

      this.config.storage.setItem(fullKey, valueWithExpires);
    } catch (error) {
      console.warn("Cache set error:", error);
    }
  }

  /**
   * Remove data from cache
   */
  remove(key: string): void {
    if (typeof window === "undefined") {
      return; // SSR environment
    }

    try {
      const fullKey = this.getKey(key);
      this.config.storage.removeItem(fullKey);
    } catch (error) {
      console.warn("Cache remove error:", error);
    }
  }

  /**
   * Clear all cache data with the current prefix
   */
  clear(): void {
    if (typeof window === "undefined") {
      return; // SSR environment
    }

    try {
      const keys = Object.keys(this.config.storage);
      const prefix = `${this.config.prefix}_`;

      keys.forEach((key) => {
        if (key.startsWith(prefix)) {
          this.config.storage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn("Cache clear error:", error);
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Get or set pattern (fetch if not in cache)
   */
  async getOrSet<T>(key: string, fetcher: () => Promise<T> | T, ttlSeconds?: number): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await fetcher();
    this.set(key, value, ttlSeconds);
    return value;
  }
}

// Create default cache instance
export const cache = new CacheManager();

// Legacy API for backward compatibility (moved from ui/cache.ts)
export const cacheGet = (key: string): string | null => cache.get<string>(key);
export const cacheSet = (key: string, value: string, expiresAt: number): void => {
  const ttl = expiresAt === -1 ? -1 : expiresAt - Math.floor(Date.now() / 1000);
  cache.set(key, value, ttl);
};
export const cacheRemove = (key: string): void => cache.remove(key);
export const cacheClear = (): void => cache.clear();
