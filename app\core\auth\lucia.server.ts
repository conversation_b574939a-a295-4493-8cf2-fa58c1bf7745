import { <PERSON> } from "lucia";
import { DrizzlePostgreSQLAdapter } from "@lucia-auth/adapter-drizzle";
import { Google } from "arctic";
import { hash, verify } from "@node-rs/argon2";
import { generateIdFromEntropySize } from "lucia";
import { eq } from "drizzle-orm";

import { createDbFromEnv } from "~/core/db";
import { sessions, users } from "~/core/db/schemas/users";

const db = createDbFromEnv();

const adapter = new DrizzlePostgreSQLAdapter(db, sessions, users);

export const lucia = new Lucia(adapter, {
  sessionCookie: {
    name: "auth_session",
    expires: false,
    attributes: {
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      httpOnly: true,
    },
  },
  getUserAttributes: (attributes) => {
    return {
      id: attributes.id,
      email: attributes.email,
      name: attributes.name,
      avatar: attributes.avatar,
      emailVerified: attributes.emailVerified,
      googleId: attributes.googleId,
    };
  },
});

// Google OAuth configuration
export const google = new Google(
  process.env.GOOGLE_CLIENT_ID!,
  process.env.GOOGLE_CLIENT_SECRET!,
  `${process.env.APP_URL || "http://localhost:3000"}/auth/google/callback`
);

// Password hashing utilities
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, {
    memoryCost: 19456,
    timeCost: 2,
    outputLen: 32,
    parallelism: 1,
  });
}

export async function verifyPassword(
  hash: string,
  password: string
): Promise<boolean> {
  return await verify(hash, password);
}

// User creation utilities
export async function createUser(data: {
  email: string;
  name?: string;
  passwordHash?: string;
  googleId?: string;
}) {
  const userId = generateIdFromEntropySize(10);
  
  await db.insert(users).values({
    id: userId,
    email: data.email,
    name: data.name,
    passwordHash: data.passwordHash,
    googleId: data.googleId,
    emailVerified: data.googleId ? true : false, // Auto-verify Google users
  });
  
  return { id: userId };
}

// Get user by email
export async function getUserByEmail(email: string) {
  const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
  return result[0] || null;
}

// Get user by Google ID
export async function getUserByGoogleId(googleId: string) {
  const result = await db.select().from(users).where(eq(users.googleId, googleId)).limit(1);
  return result[0] || null;
}

declare module "lucia" {
  interface Register {
    Lucia: typeof lucia;
    DatabaseUserAttributes: {
      id: string;
      email: string;
      name?: string;
      avatar?: string;
      emailVerified: boolean;
      googleId?: string;
    };
  }
}