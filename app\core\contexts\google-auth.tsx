// app/contexts/google-auth.tsx
import React, { createContext, type ReactNode, useContext, useEffect } from "react";
import { type UseOneTapLoginOptions, useOneTapLogin } from "~/core/hooks/useOneTapLogin";

interface AppContextType {
  oneTapLoading: boolean;
  oneTapError: Error | null;
  isOneTapInitialized: boolean; // From the hook
  // Potentially add user session data here later if needed globally
  // For example: user: Session['user'] | null;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppContextProviderProps {
  children: ReactNode;
  // Allow passing config options for One Tap, ultimately from environment variables
  googleClientId?: string;
  oneTapEnabledEnv?: boolean;
  promptParentId?: string;
}

export const AppContextProvider = ({
  children,
  googleClientId,
  oneTapEnabledEnv,
  promptParentId,
}: AppContextProviderProps) => {
  // Default to true if oneTapEnabledEnv is undefined, or use its value
  const isOneTapActuallyEnabled = oneTapEnabledEnv === undefined ? true : oneTapEnabledEnv;

  const oneTapOptions: UseOneTapLoginOptions = {
    clientId: googleClientId,
    oneTapEnabled: isOneTapActuallyEnabled, // Use the env var status
    promptParentId: promptParentId, // Optional
    onLoginSuccess: () => {
      console.log("AppContext: Login successful callback triggered.");
      // Potentially refresh user data or redirect
    },
    onLoginError: (error: Error) => {
      console.error("AppContext: Login error callback triggered.", error);
      // Potentially show a global error message
    },
  };

  const {
    isLoading: oneTapLoading,
    error: oneTapError,
    isOneTapInitialized,
  } = useOneTapLogin(oneTapOptions);

  // Log environment variables status
  useEffect(() => {
    if (isOneTapActuallyEnabled) {
      if (!googleClientId) {
        console.warn("AppContext: Google Client ID is missing. Google One Tap may not function.");
      }
      console.log("AppContext: Google One Tap is enabled via environment variable.");
    } else {
      console.log("AppContext: Google One Tap is disabled via environment variable.");
    }
  }, [googleClientId, isOneTapActuallyEnabled]);

  return (
    <AppContext.Provider
      value={{
        oneTapLoading,
        oneTapError,
        isOneTapInitialized,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppContextProvider");
  }
  return context;
};

// How to get env vars to the AppContextProvider in Remix:
// You would typically load these in your root loader and pass them down.
// For example, in app/root.tsx:
//
// import { json, type LoaderFunctionArgs } from "@remix-run/node";
// // ... other imports ...
// import { AppContextProvider } from "~/contexts/app"; // Import your AppContextProvider
//
// export async function loader({}: LoaderFunctionArgs) {
//   const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
//   const ONE_TAP_ENABLED = process.env.ONE_TAP_ENABLED === 'true';
//   // ... other loader data
//   return json({
//     // ... other data
//     ENV: {
//       GOOGLE_CLIENT_ID,
//       ONE_TAP_ENABLED,
//     }
//   });
// }
//
// export function Layout({ children }: { children: React.ReactNode }) {
//   const { ENV } = useLoaderData<typeof loader>();
//   // ...
//   return (
//     <html lang="en">
//       {/* ... */}
//       <body>
//         <ZustandProvider>
//           <AppContextProvider
//             googleClientId={ENV.GOOGLE_CLIENT_ID}
//             oneTapEnabledEnv={ENV.ONE_TAP_ENABLED}
//             // promptParentId="your-prompt-parent-id" // Optional
//           >
//             {children}
//             <Notifications />
//           </AppContextProvider>
//         </ZustandProvider>
//         {/* ... */}
//       </body>
//     </html>
//   );
// }
//
// This subtask only updates app/contexts/app.tsx.
// The changes to app/root.tsx to pass these ENV vars will be part of a separate step if needed,
// or assumed to be handled by the developer integrating this.
// For now, the AppContextProvider is designed to accept these as props.
