// app/routes/api.image-upload.tsx

import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { type ImageMetadata, ImageStorageService } from "~/core/storage/image-storage.server"; // Adjust path if necessary

export const config = {
  maxDuration: 30, // Allow up to 30 seconds for image uploads
};

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const R2_BUCKET = process.env.R2_BUCKET;
  if (!R2_BUCKET) {
    return json({ error: "R2 bucket not configured" }, { status: 503 });
  }

  const R2_PUBLIC_URL = process.env.R2_PUBLIC_URL as string | undefined;
  const imageService = new ImageStorageService(R2_BUCKET, R2_PUBLIC_URL);

  try {
    const formData = await request.formData();
    const file = formData.get("image") as File | null; // Input field name is "image"

    if (!file || !(file instanceof File)) {
      return json({ error: "No image file found or invalid file type." }, { status: 400 });
    }

    // Basic validation for image type (can be enhanced)
    if (!file.type.startsWith("image/")) {
      return json({ error: "Invalid file type. Only images are allowed." }, { status: 400 });
    }

    // TODO: Retrieve actual userId from authentication context
    const userId = (formData.get("userId") as string) || "anonymous-user"; // Example: get userId from form or auth
    const altText = (formData.get("altText") as string) || ""; // Example: get altText from form

    const metadata: Partial<ImageMetadata> = {
      originalName: file.name,
      contentType: file.type,
      size: file.size,
      uploaderId: userId,
      alt: altText, // Example of image-specific metadata
      // Add more metadata as needed, e.g., from formData
    };

    // Add any other metadata from formData that might be relevant
    // For example, if you pass 'tags' or 'description'
    const description = formData.get("description") as string;
    if (description) metadata.description = description;

    const uploadedImage = await imageService.uploadImage(file, metadata);

    if (!uploadedImage) {
      return json({ error: "Image upload failed. Check server logs." }, { status: 500 });
    }

    // The URL generation is handled by the service
    const imageUrl = imageService.getImageUrl(uploadedImage.key);

    return json({
      success: true,
      image: {
        key: uploadedImage.key,
        url: imageUrl,
        size: uploadedImage.size,
        contentType: uploadedImage.httpMetadata?.contentType,
        etag: uploadedImage.etag,
        customMetadata: uploadedImage.customMetadata,
      },
    });
  } catch (error) {
    console.error("Image upload action error:", error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
    return json({ error: `Image upload failed: ${errorMessage}` }, { status: 500 });
  }
}

/**
 * Optional: Loader to provide information about the upload endpoint
 */
export async function loader() {
  return json({
    message: "POST to this endpoint with a multipart/form-data 'image' field to upload an image.",
    maxFileSize: "10MB", // Example: This should align with actual R2 limits or app policy
    allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  });
}
