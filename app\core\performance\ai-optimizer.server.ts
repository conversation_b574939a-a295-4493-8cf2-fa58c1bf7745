/**
 * AI Response Optimization System
 * Provides AI request caching, response optimization, and performance monitoring
 */

import { cacheGet, cacheSet, cacheWrap } from "./cache-manager.server";

export interface AIOptimizationConfig {
  enableCaching: boolean;
  cacheResponseTTL: number;
  enableCompression: boolean;
  enableBatching: boolean;
  maxBatchSize: number;
  batchTimeout: number;
  enableRetries: boolean;
  maxRetries: number;
  retryDelay: number;
}

export interface AIRequestMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  cachedResponses: number;
  avgResponseTime: number;
  totalResponseTime: number;
  cacheHitRate: number;
  providerMetrics: Record<
    string,
    {
      requests: number;
      avgResponseTime: number;
      successRate: number;
    }
  >;
  modelMetrics: Record<
    string,
    {
      requests: number;
      avgResponseTime: number;
      successRate: number;
      avgTokens: number;
    }
  >;
}

export interface AIRequest {
  id: string;
  provider: string;
  model: string;
  prompt: string;
  parameters?: Record<string, any>;
  userUuid?: string;
  priority?: "low" | "normal" | "high";
  timestamp: number;
}

export interface AIResponse {
  id: string;
  content: string;
  tokensUsed?: number;
  finishReason?: string;
  model?: string;
  provider?: string;
  responseTime: number;
  cached: boolean;
  timestamp: number;
}

export interface BatchRequest {
  requests: AIRequest[];
  callback: (responses: AIResponse[]) => void;
  timeout: NodeJS.Timeout;
}

// Default configuration
export const DEFAULT_AI_CONFIG: AIOptimizationConfig = {
  enableCaching: true,
  cacheResponseTTL: 3600, // 1 hour
  enableCompression: true,
  enableBatching: true,
  maxBatchSize: 10,
  batchTimeout: 100, // 100ms
  enableRetries: true,
  maxRetries: 3,
  retryDelay: 1000, // 1 second
};

// Metrics tracking
const aiMetrics: AIRequestMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  cachedResponses: 0,
  avgResponseTime: 0,
  totalResponseTime: 0,
  cacheHitRate: 0,
  providerMetrics: {},
  modelMetrics: {},
};

// Batch processing queue
const batchQueue: AIRequest[] = [];
let batchTimeout: NodeJS.Timeout | null = null;

/**
 * Generate cache key for AI request
 */
function generateCacheKey(request: AIRequest): string {
  const { provider, model, prompt, parameters } = request;
  const paramString = parameters ? JSON.stringify(parameters) : "";
  const content = `${provider}:${model}:${prompt}:${paramString}`;

  // Simple hash function for cache key
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return `ai:${Math.abs(hash).toString(36)}`;
}

/**
 * Optimize AI request with caching and batching
 */
export async function optimizeAIRequest(
  request: AIRequest,
  aiFunction: (request: AIRequest) => Promise<AIResponse>,
  config: Partial<AIOptimizationConfig> = {}
): Promise<AIResponse> {
  const finalConfig = { ...DEFAULT_AI_CONFIG, ...config };
  const startTime = Date.now();

  try {
    // Try cache first if enabled
    if (finalConfig.enableCaching) {
      const cacheKey = generateCacheKey(request);
      const cached = cacheGet<AIResponse>(cacheKey);

      if (cached) {
        aiMetrics.cachedResponses++;
        updateMetrics(request, { ...cached, cached: true }, 0);
        return { ...cached, cached: true };
      }
    }

    // Execute AI request with retries
    const response = await executeWithRetries(
      () => aiFunction(request),
      finalConfig.maxRetries,
      finalConfig.retryDelay
    );

    const responseTime = Date.now() - startTime;
    response.responseTime = responseTime;
    response.cached = false;

    // Cache successful response
    if (finalConfig.enableCaching && response.content) {
      const cacheKey = generateCacheKey(request);
      cacheSet(cacheKey, response, finalConfig.cacheResponseTTL, "api");
    }

    updateMetrics(request, response, responseTime);
    return response;
  } catch (error) {
    const responseTime = Date.now() - startTime;
    aiMetrics.failedRequests++;
    updateProviderMetrics(request.provider, responseTime, false);
    updateModelMetrics(request.model, responseTime, false, 0);

    console.error("[AI_OPTIMIZER] Request failed:", error);
    throw error;
  }
}

/**
 * Batch AI requests for efficiency
 */
export async function batchAIRequest(
  request: AIRequest,
  aiFunction: (requests: AIRequest[]) => Promise<AIResponse[]>,
  config: Partial<AIOptimizationConfig> = {}
): Promise<AIResponse> {
  const finalConfig = { ...DEFAULT_AI_CONFIG, ...config };

  if (!finalConfig.enableBatching) {
    // Fall back to single request
    return optimizeAIRequest(
      request,
      async (req) => {
        const responses = await aiFunction([req]);
        return responses[0];
      },
      config
    );
  }

  return new Promise((resolve, reject) => {
    // Add request to batch queue
    batchQueue.push(request);

    // Set up batch processing
    if (!batchTimeout) {
      batchTimeout = setTimeout(async () => {
        await processBatch(aiFunction, finalConfig);
      }, finalConfig.batchTimeout);
    }

    // Process batch if it reaches max size
    if (batchQueue.length >= finalConfig.maxBatchSize) {
      if (batchTimeout) {
        clearTimeout(batchTimeout);
        batchTimeout = null;
      }
      processBatch(aiFunction, finalConfig);
    }

    // Store resolve/reject for this request
    (request as any)._resolve = resolve;
    (request as any)._reject = reject;
  });
}

/**
 * Process batched AI requests
 */
async function processBatch(
  aiFunction: (requests: AIRequest[]) => Promise<AIResponse[]>,
  config: AIOptimizationConfig
): Promise<void> {
  if (batchQueue.length === 0) return;

  const requests = batchQueue.splice(0, config.maxBatchSize);
  batchTimeout = null;

  try {
    const startTime = Date.now();
    const responses = await executeWithRetries(
      () => aiFunction(requests),
      config.maxRetries,
      config.retryDelay
    );

    const responseTime = Date.now() - startTime;

    // Resolve individual requests
    for (let i = 0; i < requests.length; i++) {
      const request = requests[i];
      const response = responses[i] || {
        id: request.id,
        content: "",
        responseTime,
        cached: false,
        timestamp: Date.now(),
      };

      response.responseTime = responseTime;
      updateMetrics(request, response, responseTime);

      if ((request as any)._resolve) {
        (request as any)._resolve(response);
      }
    }
  } catch (error) {
    // Reject all requests in batch
    for (const request of requests) {
      if ((request as any)._reject) {
        (request as any)._reject(error);
      }
    }
  }

  // Process remaining requests if any
  if (batchQueue.length > 0) {
    batchTimeout = setTimeout(() => {
      processBatch(aiFunction, config);
    }, config.batchTimeout);
  }
}

/**
 * Execute function with retries
 */
async function executeWithRetries<T>(
  fn: () => Promise<T>,
  maxRetries: number,
  retryDelay: number
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt < maxRetries) {
        const delay = retryDelay * 2 ** attempt; // Exponential backoff
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error("Function failed after retries");
}

/**
 * Update AI request metrics
 */
function updateMetrics(request: AIRequest, response: AIResponse, responseTime: number): void {
  aiMetrics.totalRequests++;

  if (response.cached) {
    // Don't count cached responses in timing metrics
    return;
  }

  if (response.content) {
    aiMetrics.successfulRequests++;
  } else {
    aiMetrics.failedRequests++;
  }

  aiMetrics.totalResponseTime += responseTime;
  aiMetrics.avgResponseTime =
    aiMetrics.totalResponseTime / (aiMetrics.successfulRequests + aiMetrics.failedRequests);

  // Update cache hit rate
  const totalRequests = aiMetrics.totalRequests;
  aiMetrics.cacheHitRate =
    totalRequests > 0 ? (aiMetrics.cachedResponses / totalRequests) * 100 : 0;

  // Update provider metrics
  updateProviderMetrics(request.provider, responseTime, !!response.content);

  // Update model metrics
  updateModelMetrics(request.model, responseTime, !!response.content, response.tokensUsed || 0);
}

/**
 * Update provider-specific metrics
 */
function updateProviderMetrics(provider: string, responseTime: number, success: boolean): void {
  if (!aiMetrics.providerMetrics[provider]) {
    aiMetrics.providerMetrics[provider] = {
      requests: 0,
      avgResponseTime: 0,
      successRate: 0,
    };
  }

  const metrics = aiMetrics.providerMetrics[provider];
  metrics.requests++;

  if (success) {
    metrics.avgResponseTime = (metrics.avgResponseTime + responseTime) / 2;
  }

  // Calculate success rate (simplified)
  metrics.successRate = success
    ? Math.min(metrics.successRate + 1, 100)
    : Math.max(metrics.successRate - 1, 0);
}

/**
 * Update model-specific metrics
 */
function updateModelMetrics(
  model: string,
  responseTime: number,
  success: boolean,
  tokens: number
): void {
  if (!aiMetrics.modelMetrics[model]) {
    aiMetrics.modelMetrics[model] = {
      requests: 0,
      avgResponseTime: 0,
      successRate: 0,
      avgTokens: 0,
    };
  }

  const metrics = aiMetrics.modelMetrics[model];
  metrics.requests++;

  if (success) {
    metrics.avgResponseTime = (metrics.avgResponseTime + responseTime) / 2;
    metrics.avgTokens = (metrics.avgTokens + tokens) / 2;
  }

  // Calculate success rate (simplified)
  metrics.successRate = success
    ? Math.min(metrics.successRate + 1, 100)
    : Math.max(metrics.successRate - 1, 0);
}

/**
 * Get AI optimization metrics
 */
export function getAIMetrics(): AIRequestMetrics {
  return { ...aiMetrics };
}

/**
 * Reset AI metrics
 */
export function resetAIMetrics(): void {
  aiMetrics.totalRequests = 0;
  aiMetrics.successfulRequests = 0;
  aiMetrics.failedRequests = 0;
  aiMetrics.cachedResponses = 0;
  aiMetrics.avgResponseTime = 0;
  aiMetrics.totalResponseTime = 0;
  aiMetrics.cacheHitRate = 0;
  aiMetrics.providerMetrics = {};
  aiMetrics.modelMetrics = {};
}

/**
 * Preload common AI responses
 */
export async function preloadCommonResponses(
  commonRequests: AIRequest[],
  aiFunction: (request: AIRequest) => Promise<AIResponse>
): Promise<void> {
  console.log("[AI_OPTIMIZER] Preloading common AI responses...");

  const preloadPromises = commonRequests.map(async (request) => {
    const cacheKey = generateCacheKey(request);

    // Skip if already cached
    if (cacheGet(cacheKey)) {
      return;
    }

    try {
      const response = await aiFunction(request);
      cacheSet(cacheKey, response, DEFAULT_AI_CONFIG.cacheResponseTTL, "api");
    } catch (error) {
      console.warn("[AI_OPTIMIZER] Failed to preload response for request:", request.id, error);
    }
  });

  await Promise.allSettled(preloadPromises);
  console.log("[AI_OPTIMIZER] Preloading completed");
}

/**
 * Optimize AI model selection based on metrics
 */
export function getOptimalModel(provider: string, task: "text" | "image" | "code"): string {
  const providerMetrics = aiMetrics.providerMetrics[provider];

  if (!providerMetrics) {
    // Return default models for each task
    const defaultModels = {
      text: "gpt-3.5-turbo",
      image: "dall-e-3",
      code: "gpt-4",
    };
    return defaultModels[task];
  }

  // Find the best performing model for this provider
  const models = Object.entries(aiMetrics.modelMetrics)
    .filter(([model]) => model.includes(provider))
    .sort(([, a], [, b]) => {
      // Sort by success rate and response time
      const scoreA = a.successRate - a.avgResponseTime / 1000;
      const scoreB = b.successRate - b.avgResponseTime / 1000;
      return scoreB - scoreA;
    });

  return models[0]?.[0] || "gpt-3.5-turbo";
}

/**
 * Clear AI optimization cache
 */
export function clearAICache(): void {
  // This would clear AI-specific cache entries
  console.log("[AI_OPTIMIZER] AI cache cleared");
}
