import { json, type LoaderFunctionArgs, type MetaFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  ArrowRight,
  BarChart3,
  Check,
  Code,
  Image,
  MessageSquare,
  Shield,
  Sparkles,
  Users,
  Zap,
} from "lucide-react";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { getLandingPageConfig } from "~/config/landing";
import { siteConfig } from "~/config/seo";
import { generateStructuredData } from "~/core/seo/seo";

export const meta: MetaFunction = () => {
  const title = "ChatGPT - AI Assistant for Everyone";
  const description =
    "Experience the power of AI with ChatGPT. Get instant help with writing, coding, analysis, and creative tasks through natural conversations.";

  return [
    { title },
    { name: "description", content: description },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    {
      "script:ld+json": generateStructuredData({
        type: "WebSite",
        name: title,
        description,
        url: `${siteConfig.url}/landingpage`,
      }),
    },
  ];
};

export async function loader({ context }: LoaderFunctionArgs) {
  const config = getLandingPageConfig();

  return json({
    config,
  });
}

const features = [
  {
    icon: <MessageSquare className="w-6 h-6" />,
    title: "Natural Conversations",
    description: "Chat with AI that understands context and provides helpful, detailed responses.",
  },
  {
    icon: <Code className="w-6 h-6" />,
    title: "Code Generation",
    description: "Write, debug, and explain code in dozens of programming languages.",
  },
  {
    icon: <Image className="w-6 h-6" />,
    title: "Creative Content",
    description: "Generate images, write stories, create content, and unleash your creativity.",
  },
  {
    icon: <BarChart3 className="w-6 h-6" />,
    title: "Data Analysis",
    description: "Analyze data, create charts, and gain insights from complex information.",
  },
];

const useCases = [
  {
    title: "Students & Researchers",
    description:
      "Get help with homework, research papers, and learning new concepts across any subject.",
    examples: ["Explain complex topics", "Research assistance", "Writing help", "Problem solving"],
  },
  {
    title: "Developers & Engineers",
    description:
      "Accelerate your coding with AI-powered assistance for development and technical tasks.",
    examples: ["Code generation", "Bug fixing", "Code review", "Documentation"],
  },
  {
    title: "Writers & Creators",
    description:
      "Enhance your creative process with AI assistance for writing and content creation.",
    examples: ["Content ideation", "Copy editing", "Creative writing", "Marketing copy"],
  },
  {
    title: "Business Professionals",
    description:
      "Streamline your workflow with AI help for analysis, communication, and productivity.",
    examples: ["Data analysis", "Email drafting", "Presentations", "Strategy planning"],
  },
];

const stats = [
  { value: "100M+", label: "Users worldwide" },
  { value: "10B+", label: "Conversations" },
  { value: "175+", label: "Countries" },
  { value: "99.9%", label: "Uptime" },
];

const testimonials = [
  {
    name: "Sarah Chen",
    role: "Software Developer",
    content:
      "ChatGPT has revolutionized my coding workflow. It helps me debug faster and learn new technologies efficiently.",
  },
  {
    name: "Mark Rodriguez",
    role: "Content Creator",
    content:
      "The creative assistance is incredible. It helps me brainstorm ideas and overcome writer's block consistently.",
  },
  {
    name: "Dr. Emily Johnson",
    role: "Researcher",
    content:
      "Perfect for research assistance and explaining complex concepts to students. It's like having a knowledgeable colleague available 24/7.",
  },
];

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-semibold text-foreground">ChatGPT</span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <a href="/about">About</a>
            </Button>
            <Button variant="ghost" asChild>
              <a href="/pricing">Pricing</a>
            </Button>
            <Button variant="ghost" asChild>
              <a href="/auth/login">Log in</a>
            </Button>
            <Button asChild>
              <a href="/auth/register">Sign up</a>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-8">
            <Sparkles className="w-10 h-10 text-primary-foreground" />
          </div>

          <h1 className="text-5xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
            AI that understands
            <br />
            <span className="text-primary">what you need</span>
          </h1>

          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12 leading-relaxed">
            ChatGPT is an AI assistant that can help with writing, learning, brainstorming, and much
            more. Have a conversation with AI that feels natural and get instant help with your
            tasks.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button
              size="lg"
              asChild
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 text-lg"
            >
              <a href="/" className="inline-flex items-center gap-2">
                Try ChatGPT
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
            <Button size="lg" variant="outline" asChild className="px-8 py-4 text-lg">
              <a href="/about">Learn more</a>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Capabilities that adapt to your needs
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From creative writing to complex problem-solving, ChatGPT helps you accomplish more.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="p-6 text-center">
                <CardContent className="p-0">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <div className="text-primary">{feature.icon}</div>
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Trusted by millions across every industry
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From students to professionals, people use ChatGPT to enhance their productivity and
              creativity.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <Card key={index} className="p-8">
                <CardContent className="p-0">
                  <h3 className="text-xl font-semibold text-foreground mb-3">{useCase.title}</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {useCase.description}
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    {useCase.examples.map((example, exampleIndex) => (
                      <div key={exampleIndex} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{example}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-6 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">What people are saying</h2>
            <p className="text-xl text-muted-foreground">
              Real experiences from ChatGPT users worldwide
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="p-0">
                  <p className="text-muted-foreground mb-6 leading-relaxed italic">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <div className="bg-primary/5 rounded-3xl p-16">
            <h2 className="text-4xl font-bold text-foreground mb-6">Ready to get started?</h2>
            <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto">
              Join millions of users who are already using ChatGPT to enhance their productivity,
              creativity, and learning.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                asChild
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 text-lg"
              >
                <a href="/" className="inline-flex items-center gap-2">
                  Start chatting for free
                  <ArrowRight className="w-5 h-5" />
                </a>
              </Button>
              <Button size="lg" variant="outline" asChild className="px-8 py-4 text-lg">
                <a href="/pricing">View pricing</a>
              </Button>
            </div>

            <p className="text-sm text-muted-foreground mt-6">
              No credit card required • Free to get started
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border bg-muted/20 py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-lg font-semibold text-foreground">ChatGPT</span>
              </div>
              <p className="text-muted-foreground">
                AI assistant that understands and helps with your everyday tasks.
              </p>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Product</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="/" className="hover:text-foreground">
                    Chat
                  </a>
                </li>
                <li>
                  <a href="/about" className="hover:text-foreground">
                    About
                  </a>
                </li>
                <li>
                  <a href="/pricing" className="hover:text-foreground">
                    Pricing
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-foreground">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground">
                    Status
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Company</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-foreground">
                    Privacy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground">
                    Terms
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground">
                    Security
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-muted-foreground text-sm">© 2024 ChatGPT. All rights reserved.</p>
              <div className="flex items-center gap-6 mt-4 md:mt-0">
                <a href="#" className="text-muted-foreground hover:text-foreground">
                  <span className="sr-only">Twitter</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                </a>
                <a href="#" className="text-muted-foreground hover:text-foreground">
                  <span className="sr-only">GitHub</span>
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
