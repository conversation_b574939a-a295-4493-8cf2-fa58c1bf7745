import type { EmailTemplate } from "../templating.server";
import { populateTemplate } from "../templating.server";

export interface PasswordResetTemplateVariables {
  name: string;
  resetLink: string;
  [key: string]: string | number | boolean | undefined;
}

const subjectTemplate = "Your Password Reset Request";
const htmlTemplate = `
  <h1>Password Reset Request</h1>
  <p>Hello {{name}},</p>
  <p>We received a request to reset your password. If you made this request, please click the link below:</p>
  <p><a href="{{resetLink}}">Reset Password</a></p>
  <p>If you didn't request a password reset, please ignore this email.</p>
  <br>
  <p>Thanks,</p>
  <p>The Platform Team</p>
`;
const textTemplate = `
  Password Reset Request

  Hello {{name}},
  We received a request to reset your password. If you made this request, please use the following link:
  {{resetLink}}

  If you didn't request a password reset, please ignore this email.

  Thanks,
  The Platform Team
`;

export const passwordResetTemplate: EmailTemplate<PasswordResetTemplateVariables> = {
  subject: (vars) => populateTemplate(subjectTemplate, vars),
  html: (vars) => populateTemplate(htmlTemplate, vars),
  text: (vars) => populateTemplate(textTemplate, vars),
};
