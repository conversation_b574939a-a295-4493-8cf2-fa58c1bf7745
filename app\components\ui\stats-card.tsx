/**
 * Statistics Card Component
 * Reusable card component for displaying metrics and statistics
 */

import { TrendingDown, TrendingUp } from "lucide-react";
import type { ReactNode } from "react";
import { cn } from "~/core/utils/cn";
import { Badge } from "./badge";
import { Card, CardContent, CardHeader, CardTitle } from "./card";

export interface TrendData {
  value: number;
  isPositive: boolean;
  label: string;
  period?: string; // e.g., "vs last month"
}

export interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: ReactNode;
  iconColor?: "blue" | "green" | "purple" | "orange" | "red" | "gray";
  trend?: TrendData;
  badge?: {
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  className?: string;
  onClick?: () => void;
  loading?: boolean;
}

const iconColorClasses = {
  blue: "bg-blue-500 text-white",
  green: "bg-green-500 text-white",
  purple: "bg-purple-500 text-white",
  orange: "bg-orange-500 text-white",
  red: "bg-red-500 text-white",
  gray: "bg-gray-500 text-white",
};

export function StatsCard({
  title,
  value,
  subtitle,
  icon,
  iconColor = "blue",
  trend,
  badge,
  className,
  onClick,
  loading = false,
}: StatsCardProps) {
  const CardWrapper = onClick ? "button" : "div";

  return (
    <Card
      className={cn(
        "relative overflow-hidden transition-all duration-200",
        onClick && "cursor-pointer hover:shadow-md hover:scale-[1.02]",
        className
      )}
      onClick={onClick}
      {...(onClick && { role: "button", tabIndex: 0 })}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {icon && (
          <div
            className={cn(
              "h-8 w-8 rounded-md flex items-center justify-center",
              iconColorClasses[iconColor]
            )}
          >
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {/* Main Value */}
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold tracking-tight">
              {loading ? <div className="h-8 w-20 bg-muted animate-pulse rounded" /> : value}
            </div>
            {badge && (
              <Badge variant={badge.variant || "secondary"} className="text-xs">
                {badge.text}
              </Badge>
            )}
          </div>

          {/* Subtitle */}
          {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}

          {/* Trend */}
          {trend && !loading && (
            <div className="flex items-center text-xs">
              {trend.isPositive ? (
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span
                className={cn("font-medium", trend.isPositive ? "text-green-600" : "text-red-600")}
              >
                {trend.isPositive ? "+" : ""}
                {trend.value}%
              </span>
              <span className="ml-1 text-muted-foreground">
                {trend.label}
                {trend.period && ` ${trend.period}`}
              </span>
            </div>
          )}

          {loading && trend && <div className="h-4 w-24 bg-muted animate-pulse rounded" />}
        </div>
      </CardContent>
    </Card>
  );
}

// Preset variations for common use cases
export function MetricCard({
  title,
  value,
  icon,
  trend,
  formatter = "number",
  ...props
}: Omit<StatsCardProps, "value"> & {
  value: number;
  formatter?: "number" | "currency" | "percentage";
}) {
  const formatValue = (val: number) => {
    switch (formatter) {
      case "currency":
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(val);
      case "percentage":
        return `${val}%`;
      default:
        return val.toLocaleString();
    }
  };

  return (
    <StatsCard title={title} value={formatValue(value)} icon={icon} trend={trend} {...props} />
  );
}

export function LoadingStatsCard({
  title,
  icon,
  iconColor,
}: Pick<StatsCardProps, "title" | "icon" | "iconColor">) {
  return <StatsCard title={title} value="" icon={icon} iconColor={iconColor} loading={true} />;
}

// Grid layout helper
export function StatsGrid({
  children,
  columns = 4,
  className,
}: {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  return <div className={cn("grid gap-4", gridClasses[columns], className)}>{children}</div>;
}
