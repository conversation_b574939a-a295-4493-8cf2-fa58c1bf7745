/**
 * API Route: Usage Analytics
 * Provides detailed usage analytics and statistics for users
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { respData, respErr } from "~/core/api/resp";
import { requireUser } from "~/core/auth/middleware.server";
import { createDb } from "~/core/db/db";
import { getRecentUsage, getUserAnalytics } from "~/core/services/analytics.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Check authentication
    const user = await requireUser(request);

    // Create database connection
    if (!process.env.DATABASE_URL && !context.db) {
      return respErr("Database not available");
    }
    const db = context.db || createDb(process.env.DATABASE_URL!);

    // Parse query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get("period") || "7"; // days
    const includeRecent = url.searchParams.get("recent") === "true";

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(period, 10));

    // Get usage analytics
    const analytics = await getUserAnalytics(user.uuid, { startDate, endDate }, db);

    // Get recent usage if requested
    let recentUsage: any[] = [];
    if (includeRecent) {
      recentUsage = await getRecentUsage(user.uuid, 20, db);
    }

    return respData({
      analytics,
      recentUsage,
      period: {
        days: parseInt(period, 10),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error getting usage analytics:", error);
    return respErr(error instanceof Error ? error.message : "Failed to get usage analytics");
  }
}
