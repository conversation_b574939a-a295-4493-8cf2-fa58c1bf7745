/**
 * AI Test Runner - Automated testing for all AI providers
 */

import type { AIProvider } from "./ai-providers";
import { getAvailableProviders, validateProviderModel } from "./ai-providers";

export interface TestCase {
  id: string;
  name: string;
  description: string;
  provider: AIProvider;
  model: string;
  testType: "text" | "stream" | "image";
  prompt: string;
  expectedDuration?: number; // in milliseconds
  shouldSucceed: boolean;
}

export interface TestResult {
  testCase: TestCase;
  success: boolean;
  duration: number;
  response?: any;
  error?: string;
  timestamp: string;
}

export interface TestSuiteResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  duration: number;
  results: TestResult[];
  summary: {
    byProvider: Record<string, { passed: number; failed: number }>;
    byTestType: Record<string, { passed: number; failed: number }>;
  };
}

/**
 * Default test cases for comprehensive AI testing
 */
export const DEFAULT_TEST_CASES: TestCase[] = [
  // OpenAI Tests
  {
    id: "openai-gpt4o-text",
    name: "OpenAI GPT-4o Text Generation",
    description: "Test basic text generation with GPT-4o",
    provider: "openai",
    model: "gpt-4o",
    testType: "text",
    prompt: "Explain quantum computing in one sentence.",
    expectedDuration: 5000,
    shouldSucceed: true,
  },
  {
    id: "openai-gpt4o-mini-text",
    name: "OpenAI GPT-4o Mini Text Generation",
    description: "Test text generation with GPT-4o Mini",
    provider: "openai",
    model: "gpt-4o-mini",
    testType: "text",
    prompt: "What is the capital of France?",
    expectedDuration: 3000,
    shouldSucceed: true,
  },
  {
    id: "openai-dalle3-image",
    name: "OpenAI DALL-E 3 Image Generation",
    description: "Test image generation with DALL-E 3",
    provider: "openai",
    model: "dall-e-3",
    testType: "image",
    prompt: "A serene mountain landscape at sunset",
    expectedDuration: 15000,
    shouldSucceed: true,
  },

  // DeepSeek Tests
  {
    id: "deepseek-chat-text",
    name: "DeepSeek Chat Text Generation",
    description: "Test text generation with DeepSeek Chat",
    provider: "deepseek",
    model: "deepseek-chat",
    testType: "text",
    prompt: "Write a haiku about artificial intelligence.",
    expectedDuration: 4000,
    shouldSucceed: true,
  },
  {
    id: "deepseek-r1-reasoning",
    name: "DeepSeek R1 Reasoning",
    description: "Test reasoning capabilities with DeepSeek R1",
    provider: "deepseek",
    model: "deepseek-r1",
    testType: "text",
    prompt:
      "Solve this logic puzzle: If all roses are flowers and some flowers are red, can we conclude that some roses are red?",
    expectedDuration: 8000,
    shouldSucceed: true,
  },

  // OpenRouter Tests
  {
    id: "openrouter-claude-text",
    name: "OpenRouter Claude Text Generation",
    description: "Test text generation via OpenRouter with Claude",
    provider: "openrouter",
    model: "anthropic/claude-3.5-sonnet",
    testType: "text",
    prompt: "Explain the concept of recursion in programming.",
    expectedDuration: 6000,
    shouldSucceed: true,
  },
  {
    id: "openrouter-deepseek-r1",
    name: "OpenRouter DeepSeek R1",
    description: "Test DeepSeek R1 via OpenRouter",
    provider: "openrouter",
    model: "deepseek/deepseek-r1",
    testType: "text",
    prompt: "What are the ethical implications of AI in healthcare?",
    expectedDuration: 10000,
    shouldSucceed: true,
  },

  // SiliconFlow Tests
  {
    id: "siliconflow-deepseek-r1",
    name: "SiliconFlow DeepSeek R1",
    description: "Test DeepSeek R1 via SiliconFlow",
    provider: "siliconflow",
    model: "deepseek-ai/DeepSeek-R1",
    testType: "text",
    prompt: "Compare and contrast machine learning and deep learning.",
    expectedDuration: 7000,
    shouldSucceed: true,
  },
  {
    id: "siliconflow-qwen-text",
    name: "SiliconFlow Qwen Text Generation",
    description: "Test text generation with Qwen model",
    provider: "siliconflow",
    model: "Qwen/Qwen2.5-72B-Instruct",
    testType: "text",
    prompt: "Describe the process of photosynthesis.",
    expectedDuration: 5000,
    shouldSucceed: true,
  },

  // Replicate Tests
  {
    id: "replicate-llama-text",
    name: "Replicate Llama Text Generation",
    description: "Test text generation with Llama via Replicate",
    provider: "replicate",
    model: "meta/llama-2-70b-chat",
    testType: "text",
    prompt: "What are the benefits of renewable energy?",
    expectedDuration: 8000,
    shouldSucceed: true,
  },

  // Stream Tests
  {
    id: "openai-gpt4o-stream",
    name: "OpenAI GPT-4o Streaming",
    description: "Test streaming text generation with GPT-4o",
    provider: "openai",
    model: "gpt-4o",
    testType: "stream",
    prompt: "Tell me a short story about a robot learning to paint.",
    expectedDuration: 10000,
    shouldSucceed: true,
  },
  {
    id: "deepseek-chat-stream",
    name: "DeepSeek Chat Streaming",
    description: "Test streaming text generation with DeepSeek",
    provider: "deepseek",
    model: "deepseek-chat",
    testType: "stream",
    prompt: "Explain the history of the internet in detail.",
    expectedDuration: 12000,
    shouldSucceed: true,
  },

  // Error Cases
  {
    id: "invalid-model-test",
    name: "Invalid Model Test",
    description: "Test error handling with invalid model",
    provider: "openai",
    model: "invalid-model-name",
    testType: "text",
    prompt: "This should fail.",
    shouldSucceed: false,
  },
];

/**
 * Run a single test case
 */
export async function runTestCase(testCase: TestCase, baseUrl: string): Promise<TestResult> {
  const startTime = Date.now();

  try {
    const endpoint = getEndpointForTestType(testCase.testType);
    const requestBody = buildRequestBody(testCase);

    const response = await fetch(`${baseUrl}${endpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    const duration = Date.now() - startTime;
    const responseData = await response.json();

    const success = testCase.shouldSucceed ? response.ok : !response.ok;

    return {
      testCase,
      success,
      duration,
      response: responseData,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    const success = !testCase.shouldSucceed; // If we expected failure, error is success

    return {
      testCase,
      success,
      duration,
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Run the complete test suite
 */
export async function runTestSuite(
  testCases: TestCase[] = DEFAULT_TEST_CASES,
  baseUrl = "http://localhost:5173"
): Promise<TestSuiteResult> {
  const startTime = Date.now();
  const results: TestResult[] = [];

  console.log(`🧪 Starting AI Test Suite with ${testCases.length} test cases...`);

  for (const testCase of testCases) {
    console.log(`Running: ${testCase.name}`);
    const result = await runTestCase(testCase, baseUrl);
    results.push(result);

    // Log result
    if (result.success) {
      console.log(`✅ ${testCase.name} - PASSED (${result.duration}ms)`);
    } else {
      console.log(`❌ ${testCase.name} - FAILED (${result.duration}ms)`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    }

    // Small delay between tests to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  const duration = Date.now() - startTime;
  const passedTests = results.filter((r) => r.success).length;
  const failedTests = results.length - passedTests;

  // Generate summary
  const byProvider: Record<string, { passed: number; failed: number }> = {};
  const byTestType: Record<string, { passed: number; failed: number }> = {};

  results.forEach((result) => {
    const provider = result.testCase.provider;
    const testType = result.testCase.testType;

    if (!byProvider[provider]) {
      byProvider[provider] = { passed: 0, failed: 0 };
    }
    if (!byTestType[testType]) {
      byTestType[testType] = { passed: 0, failed: 0 };
    }

    if (result.success) {
      byProvider[provider].passed++;
      byTestType[testType].passed++;
    } else {
      byProvider[provider].failed++;
      byTestType[testType].failed++;
    }
  });

  const testSuiteResult: TestSuiteResult = {
    totalTests: results.length,
    passedTests,
    failedTests,
    duration,
    results,
    summary: {
      byProvider,
      byTestType,
    },
  };

  console.log("\n🎯 Test Suite Complete:");
  console.log(`   Total: ${testSuiteResult.totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${failedTests}`);
  console.log(`   Duration: ${duration}ms`);

  return testSuiteResult;
}

/**
 * Helper functions
 */
function getEndpointForTestType(testType: string): string {
  switch (testType) {
    case "text":
      return "/api/ai/generate-text";
    case "stream":
      return "/api/ai/stream-text";
    case "image":
      return "/api/ai/generate-image";
    default:
      throw new Error(`Unknown test type: ${testType}`);
  }
}

function buildRequestBody(testCase: TestCase): any {
  const baseBody = {
    prompt: testCase.prompt,
    provider: testCase.provider,
    model: testCase.model,
  };

  if (testCase.testType === "image") {
    return {
      ...baseBody,
      size: "1024x1024",
      n: 1,
    };
  }

  return baseBody;
}

/**
 * Validate test environment
 */
export function validateTestEnvironment(envStatus: Record<string, boolean>): {
  isValid: boolean;
  missingKeys: string[];
  warnings: string[];
} {
  const missingKeys: string[] = [];
  const warnings: string[] = [];

  // Check required API keys
  if (!envStatus.OPENAI_API_KEY) {
    missingKeys.push("OPENAI_API_KEY");
  }
  if (!envStatus.DEEPSEEK_API_KEY) {
    missingKeys.push("DEEPSEEK_API_KEY");
  }
  if (!envStatus.OPENROUTER_API_KEY) {
    warnings.push("OPENROUTER_API_KEY missing - OpenRouter tests will fail");
  }
  if (!envStatus.SILICONFLOW_API_KEY) {
    warnings.push("SILICONFLOW_API_KEY missing - SiliconFlow tests will fail");
  }
  if (!envStatus.REPLICATE_API_TOKEN) {
    warnings.push("REPLICATE_API_TOKEN missing - Replicate tests will fail");
  }

  return {
    isValid: missingKeys.length === 0,
    missingKeys,
    warnings,
  };
}
