import { Send } from "lucide-react";
import { memo, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { cn } from "~/core/utils/cn";

interface ChatInputProps {
  onSend: (message: string) => void;
  isLoading?: boolean;
  isDisabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const ChatInput = memo(function ChatInput({
  onSend,
  isLoading = false,
  isDisabled = false,
  placeholder = "Type your message here...",
  className,
}: ChatInputProps) {
  const [input, setInput] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const message = input.trim();
    if (!message || isLoading || isDisabled) return;

    onSend(message);
    setInput("");

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = "auto";
    textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
  };

  const canSend = input.trim().length > 0 && !isLoading && !isDisabled;

  return (
    <div className={cn("border-t border-border/40 bg-background", className)}>
      <div className="container mx-auto max-w-4xl p-4">
        <form onSubmit={handleSubmit} className="relative">
          <div className="relative flex items-end gap-3">
            <div className="flex-1 relative">
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={handleTextareaChange}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={isDisabled}
                className={cn(
                  "min-h-[44px] max-h-[200px] resize-none border border-border/60 bg-background",
                  "focus:border-primary focus:ring-1 focus:ring-primary",
                  "pr-12 py-3 text-sm leading-5",
                  isDisabled && "opacity-50 cursor-not-allowed"
                )}
                rows={1}
                aria-label="Chat message input"
                aria-describedby="chat-input-help"
              />

              {/* Send button positioned inside textarea */}
              <Button
                type="submit"
                size="sm"
                disabled={!canSend}
                className={cn(
                  "absolute right-2 bottom-2 h-7 w-7 p-0",
                  "bg-primary hover:bg-primary/90 disabled:bg-muted",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                aria-label="Send message"
              >
                <Send className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Help text */}
          <div
            id="chat-input-help"
            className="flex items-center justify-between mt-2 text-xs text-muted-foreground"
          >
            <span>Press Enter to send, Shift + Enter for new line</span>
            <span className="tabular-nums">{input.length > 0 && `${input.length} characters`}</span>
          </div>
        </form>
      </div>
    </div>
  );
});

export default ChatInput;
