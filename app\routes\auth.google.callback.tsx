/**
 * Google OAuth Callback Route
 * Handles Google OAuth callback using Lucia 3 + Arctic
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { OAuth2RequestError } from "arctic";
import { google, lucia, getUserByGoogleId, createUser } from "~/core/auth/lucia.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  
  // Get stored state and code verifier from cookies
  const cookieHeader = request.headers.get("Cookie");
  const cookies = new Map();
  cookieHeader?.split(";").forEach(cookie => {
    const [name, value] = cookie.trim().split("=");
    cookies.set(name, value);
  });
  
  const storedState = cookies.get("google_oauth_state");
  const storedCodeVerifier = cookies.get("google_code_verifier");

  if (!code || !state || !storedState || !storedCodeVerifier || state !== storedState) {
    return redirect("/auth/login?error=invalid-oauth-state");
  }

  try {
    const tokens = await google.validateAuthorizationCode(code, storedCodeVerifier);
    
    // Get user info from Google
    const googleUserResponse = await fetch("https://openidconnect.googleapis.com/v1/userinfo", {
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
    });
    
    const googleUser: any = await googleUserResponse.json();
    
    // Check if user exists
    let user = await getUserByGoogleId(googleUser.sub);
    
    if (!user) {
      // Create new user
      const { id } = await createUser({
        email: googleUser.email,
        name: googleUser.name,
        googleId: googleUser.sub,
      });
      user = { id, email: googleUser.email, name: googleUser.name };
    }
    
    // Create session
    const session = await lucia.createSession(user.id, {});
    const sessionCookie = lucia.createSessionCookie(session.id);
    
    // Clear OAuth cookies and set session cookie
    const headers = new Headers();
    headers.append("Set-Cookie", sessionCookie.serialize());
    headers.append("Set-Cookie", "google_oauth_state=; Path=/; HttpOnly; Max-Age=0");
    headers.append("Set-Cookie", "google_code_verifier=; Path=/; HttpOnly; Max-Age=0");
    
    return redirect("/console", { headers });
    
  } catch (e) {
    console.error("Google OAuth error:", e);
    if (e instanceof OAuth2RequestError) {
      return redirect("/auth/login?error=invalid-oauth-code");
    }
    return redirect("/auth/login?error=google-oauth-failed");
  }
}

export async function action({ request }: ActionFunctionArgs) {
  return json({ error: "Method not allowed" }, { status: 405 });
}

// This component should never render as the loader redirects
export default function GoogleCallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
        <p className="text-gray-600">Processing Google authentication...</p>
      </div>
    </div>
  );
}
