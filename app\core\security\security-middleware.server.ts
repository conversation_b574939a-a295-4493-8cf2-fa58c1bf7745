/**
 * Security Middleware System
 * Comprehensive security controls including rate limiting, input validation, and threat detection
 */

import type { Database } from "~/core/db/db";

export interface SecurityConfig {
  rateLimiting: {
    enabled: boolean;
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Max requests per window
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
  };
  inputValidation: {
    enabled: boolean;
    maxBodySize: number; // Max request body size in bytes
    allowedContentTypes: string[];
    sanitizeHtml: boolean;
    validateSql: boolean;
  };
  security: {
    enableCors: boolean;
    enableHelmet: boolean;
    enableCsrf: boolean;
    allowedOrigins: string[];
    securityHeaders: boolean;
  };
  monitoring: {
    enabled: boolean;
    logFailedAttempts: boolean;
    alertThreshold: number;
    blockSuspiciousIPs: boolean;
  };
}

export interface SecurityContext {
  ip: string;
  userAgent: string;
  origin?: string;
  referer?: string;
  userUuid?: string;
  endpoint: string;
  method: string;
  timestamp: Date;
}

export interface SecurityResult {
  allowed: boolean;
  reason?: string;
  action?: "block" | "warn" | "monitor";
  rateLimit?: {
    remaining: number;
    resetTime: Date;
    blocked: boolean;
  };
}

export interface ThreatDetection {
  sqlInjection: boolean;
  xssAttempt: boolean;
  pathTraversal: boolean;
  suspiciousPatterns: boolean;
  rateLimitExceeded: boolean;
  invalidInput: boolean;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  rateLimiting: {
    enabled: true,
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },
  inputValidation: {
    enabled: true,
    maxBodySize: 10 * 1024 * 1024, // 10MB
    allowedContentTypes: [
      "application/json",
      "application/x-www-form-urlencoded",
      "multipart/form-data",
      "text/plain",
    ],
    sanitizeHtml: true,
    validateSql: true,
  },
  security: {
    enableCors: true,
    enableHelmet: true,
    enableCsrf: true,
    allowedOrigins: ["http://localhost:3000", "https://localhost:3000"],
    securityHeaders: true,
  },
  monitoring: {
    enabled: true,
    logFailedAttempts: true,
    alertThreshold: 10, // Alert after 10 failed attempts
    blockSuspiciousIPs: true,
  },
};

/**
 * Extract security context from request
 */
export function extractSecurityContext(request: Request, userUuid?: string): SecurityContext {
  const url = new URL(request.url);
  const headers = request.headers;

  return {
    ip: getClientIP(request),
    userAgent: headers.get("user-agent") || "unknown",
    origin: headers.get("origin") || undefined,
    referer: headers.get("referer") || undefined,
    userUuid,
    endpoint: url.pathname,
    method: request.method,
    timestamp: new Date(),
  };
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: Request): string {
  const headers = request.headers;

  // Check various headers for IP address
  const ipHeaders = [
    "cf-connecting-ip", // Cloudflare
    "x-forwarded-for",
    "x-real-ip",
    "x-client-ip",
    "x-forwarded",
    "x-cluster-client-ip",
    "forwarded-for",
    "forwarded",
  ];

  for (const header of ipHeaders) {
    const value = headers.get(header);
    if (value) {
      // Handle comma-separated IPs (take the first one)
      const ip = value.split(",")[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }

  return "unknown";
}

/**
 * Validate IP address format
 */
export function isValidIP(ip: string): boolean {
  const ipv4Regex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Detect potential security threats in request
 */
export function detectThreats(request: Request, body?: string): ThreatDetection {
  const url = new URL(request.url);
  const queryString = url.search;
  const userAgent = request.headers.get("user-agent") || "";
  const contentToCheck = [queryString, body, userAgent].filter(Boolean).join(" ");

  return {
    sqlInjection: detectSQLInjection(contentToCheck),
    xssAttempt: detectXSS(contentToCheck),
    pathTraversal: detectPathTraversal(url.pathname),
    suspiciousPatterns: detectSuspiciousPatterns(contentToCheck),
    rateLimitExceeded: false, // Will be set by rate limiter
    invalidInput: false, // Will be set by input validator
  };
}

/**
 * Detect SQL injection attempts
 */
export function detectSQLInjection(input: string): boolean {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /('|(\\')|(;)|(--)|(\s*(or|and)\s*\w+\s*=\s*\w+))/i,
    /(UNION\s+(ALL\s+)?SELECT)/i,
    /(\bEXEC\s*\()/i,
  ];

  return sqlPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect XSS attempts
 */
export function detectXSS(input: string): boolean {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[^>]*>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
  ];

  return xssPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect path traversal attempts
 */
export function detectPathTraversal(path: string): boolean {
  const traversalPatterns = [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
    /\.\.%2f/gi,
    /\.\.%5c/gi,
  ];

  return traversalPatterns.some((pattern) => pattern.test(path));
}

/**
 * Detect suspicious patterns
 */
export function detectSuspiciousPatterns(input: string): boolean {
  const suspiciousPatterns = [
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /window\.location/gi,
    /base64_decode/gi,
    /file_get_contents/gi,
    /system\s*\(/gi,
    /exec\s*\(/gi,
    /shell_exec/gi,
    /passthru/gi,
  ];

  return suspiciousPatterns.some((pattern) => pattern.test(input));
}

/**
 * Sanitize HTML content
 */
export function sanitizeHtml(input: string): string {
  // Remove script tags and their content
  let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

  // Remove dangerous attributes
  sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, "");
  sanitized = sanitized.replace(/javascript:/gi, "");

  // Remove dangerous tags
  const dangerousTags = ["iframe", "object", "embed", "form", "input", "textarea", "button"];
  dangerousTags.forEach((tag) => {
    const regex = new RegExp(`<${tag}\\b[^<]*(?:(?!<\\/${tag}>)<[^<]*)*<\\/${tag}>`, "gi");
    sanitized = sanitized.replace(regex, "");
  });

  return sanitized;
}

/**
 * Validate request content type
 */
export function validateContentType(request: Request, allowedTypes: string[]): boolean {
  const contentType = request.headers.get("content-type");
  if (!contentType) return true; // Allow requests without content-type

  return allowedTypes.some((type) => contentType.includes(type));
}

/**
 * Validate request body size
 */
export function validateBodySize(bodySize: number, maxSize: number): boolean {
  return bodySize <= maxSize;
}

/**
 * Generate security headers
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Content-Security-Policy": [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'",
    ].join("; "),
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Permissions-Policy": ["camera=()", "microphone=()", "geolocation=()", "payment=()"].join(", "),
  };
}

/**
 * Check if IP is in blocked list
 */
export function isBlockedIP(ip: string, blockedIPs: Set<string>): boolean {
  return blockedIPs.has(ip);
}

/**
 * Check CORS origin
 */
export function validateCorsOrigin(origin: string | null, allowedOrigins: string[]): boolean {
  if (!origin) return true; // Allow requests without origin header
  return allowedOrigins.includes(origin) || allowedOrigins.includes("*");
}

/**
 * Create security error response
 */
export function createSecurityErrorResponse(
  reason: string,
  status = 403,
  headers?: Record<string, string>
): Response {
  const responseHeaders = {
    "Content-Type": "application/json",
    ...getSecurityHeaders(),
    ...headers,
  };

  return new Response(
    JSON.stringify({
      error: "Security violation",
      message: reason,
      code: status,
      timestamp: new Date().toISOString(),
    }),
    {
      status,
      headers: responseHeaders,
    }
  );
}

/**
 * Log security event
 */
export function logSecurityEvent(context: SecurityContext, event: string, details?: any): void {
  const logEntry = {
    timestamp: context.timestamp.toISOString(),
    event,
    ip: context.ip,
    userAgent: context.userAgent,
    endpoint: context.endpoint,
    method: context.method,
    userUuid: context.userUuid,
    details,
  };

  console.warn("[SECURITY]", JSON.stringify(logEntry));

  // TODO: Send to external logging service or database
}

/**
 * Main security middleware function
 */
export async function applySecurity(
  request: Request,
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG,
  userUuid?: string
): Promise<SecurityResult> {
  const context = extractSecurityContext(request, userUuid);

  try {
    // Check blocked IPs (if implemented)
    // if (config.monitoring.blockSuspiciousIPs && isBlockedIP(context.ip, blockedIPs)) {
    //   logSecurityEvent(context, 'BLOCKED_IP');
    //   return { allowed: false, reason: 'IP address is blocked', action: 'block' };
    // }

    // Validate CORS origin
    if (config.security.enableCors && context.origin) {
      if (!validateCorsOrigin(context.origin, config.security.allowedOrigins)) {
        logSecurityEvent(context, "CORS_VIOLATION", { origin: context.origin });
        return { allowed: false, reason: "CORS policy violation", action: "block" };
      }
    }

    // Validate content type
    if (config.inputValidation.enabled) {
      if (!validateContentType(request, config.inputValidation.allowedContentTypes)) {
        logSecurityEvent(context, "INVALID_CONTENT_TYPE");
        return { allowed: false, reason: "Invalid content type", action: "block" };
      }
    }

    // Check for threats in URL and headers
    const threats = detectThreats(request);
    if (
      threats.sqlInjection ||
      threats.xssAttempt ||
      threats.pathTraversal ||
      threats.suspiciousPatterns
    ) {
      logSecurityEvent(context, "THREAT_DETECTED", threats);
      return { allowed: false, reason: "Security threat detected", action: "block" };
    }

    // All checks passed
    return { allowed: true };
  } catch (error) {
    console.error("Security middleware error:", error);
    logSecurityEvent(context, "SECURITY_ERROR", { error: error.message });

    // Fail securely - block request on error
    return { allowed: false, reason: "Security check failed", action: "block" };
  }
}
