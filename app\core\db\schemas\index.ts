// Consolidated schema exports
// This file combines all domain-specific schemas into a single export

// Billing and payment schemas
export * from "./billing";
// Chat and AI schemas
export * from "./chat";
// Content management schemas
export * from "./content";
// Monitoring and analytics schemas
export * from "./monitoring";
// Shared types and enums
export * from "./shared";
// User and authentication schemas
export * from "./users";

// Legacy compatibility - re-export everything that was in the original schema.ts
// This ensures existing imports continue to work

import * as billing from "./billing";
import * as chat from "./chat";
import * as content from "./content";
import * as monitoring from "./monitoring";
import * as shared from "./shared";
import * as users from "./users";

// Create a comprehensive schema object for Drizzle
export const schema = {
  // Shared
  ...shared,

  // Users domain
  ...users,

  // Billing domain
  ...billing,

  // Chat domain
  ...chat,

  // Content domain
  ...content,

  // Monitoring domain
  ...monitoring,
};

// Default export for backward compatibility
export default schema;
