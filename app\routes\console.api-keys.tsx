/**
 * API Keys Management Page
 * Allows users to create, manage, and revoke API keys
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Activity,
  AlertTriangle,
  Calendar,
  CheckCircle,
  Copy,
  Edit3,
  Eye,
  EyeOff,
  Key,
  Plus,
  Shield,
  Trash2,
  XCircle,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { requireUser } from "~/core/auth/middleware.server";
import { createDbFromEnv } from "~/core/db";
import {
  type ApiKeyWithUsage,
  create<PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON>pi<PERSON>ey,
  getUser<PERSON>pi<PERSON>ey<PERSON>,
  revoke<PERSON>pi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>Title,
} from "~/core/services/api-key.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Check if user is authenticated using Remix Auth
    const authUser = await requireUser(request);

    const db = context.db || createDbFromEnv();

    // Use the authenticated user's UUID
    const userUuid = authUser.uuid || authUser.id;

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);

    const apiKeysData = await getUserApiKeys(userUuid, { page, limit }, db);

    return json({
      success: true,
      data: {
        apiKeys: apiKeysData.apiKeys,
        pagination: apiKeysData.pagination,
      },
    });
  } catch (error) {
    console.error("Error loading API keys:", error);
    return json({ success: false, error: "Failed to load API keys" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    // Check if user is authenticated using Remix Auth
    const authUser = await requireUser(request);

    const db = context.db || createDbFromEnv();

    // Use the authenticated user's UUID
    const userUuid = authUser.uuid || authUser.id;

    const formData = await request.formData();
    const action = formData.get("action") as string;

    switch (action) {
      case "create": {
        const title = formData.get("title") as string;
        const expiresAtStr = formData.get("expiresAt") as string;

        const expiresAt = expiresAtStr ? new Date(expiresAtStr) : undefined;

        const result = await createApiKey(
          {
            title,
            userUuid,
            expiresAt,
          },
          db
        );

        if (result.success) {
          return json({
            success: true,
            message: "API key created successfully",
            apiKey: result.apiKey,
          });
        } else {
          return json({
            success: false,
            error: result.error,
          });
        }
      }

      case "revoke": {
        const apiKeyId = parseInt(formData.get("apiKeyId") as string, 10);

        const result = await revokeApiKey(apiKeyId, userUuid, db);

        return json({
          success: result.success,
          message: result.success ? "API key revoked successfully" : result.error,
        });
      }

      case "delete": {
        const apiKeyId = parseInt(formData.get("apiKeyId") as string, 10);

        const result = await deleteApiKey(apiKeyId, userUuid, db);

        return json({
          success: result.success,
          message: result.success ? "API key deleted successfully" : result.error,
        });
      }

      case "update-title": {
        const apiKeyId = parseInt(formData.get("apiKeyId") as string, 10);
        const newTitle = formData.get("newTitle") as string;

        const result = await updateApiKeyTitle(apiKeyId, userUuid, newTitle, db);

        return json({
          success: result.success,
          message: result.success ? "API key title updated successfully" : result.error,
        });
      }

      default:
        return json({ success: false, error: "Invalid action" });
    }
  } catch (error) {
    console.error("Error processing API key action:", error);
    return json({ success: false, error: "Failed to process request" }, { status: 500 });
  }
}

export default function ApiKeysPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Set<number>>(new Set());
  const [editingKey, setEditingKey] = useState<number | null>(null);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);

  const isSubmitting = navigation.state === "submitting";

  const toggleKeyVisibility = (keyId: number) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // TODO: Show toast notification
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  const formatApiKey = (apiKey: string, isVisible: boolean) => {
    if (isVisible) {
      return apiKey;
    }
    return `${apiKey.substring(0, 7)}${"•".repeat(20)}${apiKey.substring(apiKey.length - 4)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "revoked":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "expired":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />;
      case "revoked":
        return <XCircle className="w-4 h-4" />;
      case "expired":
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  // Show new API key if just created
  if (actionData?.success && actionData?.apiKey && !newApiKey) {
    setNewApiKey(actionData.apiKey.apiKey);
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">API Keys</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Manage your API keys for accessing our services
              </p>
            </div>
            <Button onClick={() => setShowCreateForm(true)} disabled={isSubmitting}>
              <Plus className="w-4 h-4 mr-2" />
              Create API Key
            </Button>
          </div>
        </div>

        {/* Action Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        {/* New API Key Display */}
        {newApiKey && (
          <Card className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-green-800 dark:text-green-400">
                <CheckCircle className="w-5 h-5" />
                <span>API Key Created Successfully</span>
              </CardTitle>
              <CardDescription className="text-green-700 dark:text-green-300">
                Please copy your API key now. You won't be able to see it again.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 p-3 bg-white dark:bg-gray-800 rounded border">
                <code className="flex-1 font-mono text-sm">{newApiKey}</code>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(newApiKey)}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => setNewApiKey(null)}
              >
                I've copied the key
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Create API Key Form */}
        {showCreateForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Create New API Key</CardTitle>
              <CardDescription>Create a new API key to access our services</CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="action" value="create" />

                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="e.g., Production API, Development Key"
                    required
                    maxLength={100}
                  />
                  <p className="text-sm text-gray-500 mt-1">A descriptive name for this API key</p>
                </div>

                <div>
                  <Label htmlFor="expiresAt">Expiration Date (Optional)</Label>
                  <Input
                    id="expiresAt"
                    name="expiresAt"
                    type="datetime-local"
                    min={new Date().toISOString().slice(0, 16)}
                  />
                  <p className="text-sm text-gray-500 mt-1">Leave empty for no expiration</p>
                </div>

                <div className="flex space-x-3">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Creating..." : "Create API Key"}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                    Cancel
                  </Button>
                </div>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* API Keys List */}
        <Card>
          <CardHeader>
            <CardTitle>Your API Keys</CardTitle>
            <CardDescription>
              {data.apiKeys.length === 0
                ? "No API keys found"
                : `${data.apiKeys.length} API key${data.apiKeys.length === 1 ? "" : "s"} found`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.apiKeys.length === 0 ? (
              <div className="text-center py-12">
                <Key className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No API Keys</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Create your first API key to start using our services
                </p>
                <Button onClick={() => setShowCreateForm(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create API Key
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {data.apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="p-4 border rounded-lg bg-white dark:bg-gray-800">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-2">
                          {editingKey === apiKey.id ? (
                            <Form method="post" className="flex items-center space-x-2">
                              <input type="hidden" name="action" value="update-title" />
                              <input type="hidden" name="apiKeyId" value={apiKey.id} />
                              <Input
                                name="newTitle"
                                defaultValue={apiKey.title}
                                className="w-48"
                                maxLength={100}
                              />
                              <Button type="submit" size="sm">
                                Save
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => setEditingKey(null)}
                              >
                                Cancel
                              </Button>
                            </Form>
                          ) : (
                            <>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {apiKey.title}
                              </h4>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setEditingKey(apiKey.id)}
                              >
                                <Edit3 className="w-4 h-4" />
                              </Button>
                            </>
                          )}

                          <Badge className={getStatusColor(apiKey.status)}>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(apiKey.status)}
                              <span className="capitalize">{apiKey.status}</span>
                            </div>
                          </Badge>
                        </div>

                        <div className="flex items-center space-x-2 mb-3">
                          <code className="flex-1 font-mono text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded">
                            {formatApiKey(apiKey.apiKey, visibleKeys.has(apiKey.id))}
                          </code>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleKeyVisibility(apiKey.id)}
                          >
                            {visibleKeys.has(apiKey.id) ? (
                              <EyeOff className="w-4 h-4" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(apiKey.apiKey)}
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4" />
                            <span>Created {new Date(apiKey.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Activity className="w-4 h-4" />
                            <span>Last used {apiKey.lastUsedFormatted}</span>
                          </div>
                          {apiKey.expiresAt && (
                            <div className="flex items-center space-x-2">
                              <AlertTriangle className="w-4 h-4" />
                              <span>Expires {new Date(apiKey.expiresAt).toLocaleDateString()}</span>
                            </div>
                          )}
                          <div className="flex items-center space-x-2">
                            <Shield className="w-4 h-4" />
                            <span>{apiKey.usageCount || 0} requests</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        {apiKey.status === "active" && (
                          <Form method="post" className="inline">
                            <input type="hidden" name="action" value="revoke" />
                            <input type="hidden" name="apiKeyId" value={apiKey.id} />
                            <Button
                              type="submit"
                              variant="outline"
                              size="sm"
                              disabled={isSubmitting}
                            >
                              Revoke
                            </Button>
                          </Form>
                        )}

                        <Form method="post" className="inline">
                          <input type="hidden" name="action" value="delete" />
                          <input type="hidden" name="apiKeyId" value={apiKey.id} />
                          <Button
                            type="submit"
                            variant="destructive"
                            size="sm"
                            disabled={isSubmitting}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </Form>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasPrev}>
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Page {data.pagination.currentPage} of {data.pagination.totalPages}
                  </span>
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasNext}>
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Security Notice */}
        <Card className="mt-6 border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-yellow-800 dark:text-yellow-400">
              <Shield className="w-5 h-5" />
              <span>Security Best Practices</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-yellow-700 dark:text-yellow-300">
            <ul className="space-y-2 text-sm">
              <li>• Keep your API keys secure and never share them publicly</li>
              <li>• Use different API keys for different environments (development, production)</li>
              <li>• Regularly rotate your API keys for enhanced security</li>
              <li>• Set expiration dates for temporary or testing keys</li>
              <li>• Revoke any keys that may have been compromised</li>
              <li>• Monitor your API key usage regularly</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
