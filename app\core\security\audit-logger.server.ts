/**
 * Audit Logging System
 * Comprehensive security event logging and monitoring
 */

import type { Database } from "~/core/db/db";

export interface AuditEvent {
  id?: string;
  timestamp: Date;
  eventType: AuditEventType;
  severity: AuditSeverity;
  userUuid?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  endpoint: string;
  method: string;
  statusCode?: number;
  resource?: string;
  action: string;
  details?: Record<string, any>;
  success: boolean;
  errorMessage?: string;
  riskScore?: number;
}

export type AuditEventType =
  | "AUTHENTICATION"
  | "AUTHORIZATION"
  | "DATA_ACCESS"
  | "DATA_MODIFICATION"
  | "SECURITY_VIOLATION"
  | "RATE_LIMIT"
  | "API_ACCESS"
  | "ADMIN_ACTION"
  | "SYSTEM_EVENT"
  | "ERROR";

export type AuditSeverity = "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";

export interface SecurityMetrics {
  totalEvents: number;
  failedLogins: number;
  securityViolations: number;
  rateLimitExceeded: number;
  suspiciousActivity: number;
  lastHourEvents: number;
  topRiskyIPs: Array<{ ip: string; riskScore: number; eventCount: number }>;
  recentCriticalEvents: AuditEvent[];
}

// In-memory storage for audit logs (use database in production)
const auditLogs: AuditEvent[] = [];
const maxInMemoryLogs = 10000; // Keep last 10k logs in memory

/**
 * Log an audit event
 */
export async function logAuditEvent(
  event: Omit<AuditEvent, "id" | "timestamp">,
  db?: Database
): Promise<void> {
  const auditEvent: AuditEvent = {
    id: generateEventId(),
    timestamp: new Date(),
    ...event,
  };

  // Add to in-memory storage
  auditLogs.push(auditEvent);

  // Keep only the most recent logs in memory
  if (auditLogs.length > maxInMemoryLogs) {
    auditLogs.splice(0, auditLogs.length - maxInMemoryLogs);
  }

  // Log to console for immediate visibility
  logToConsole(auditEvent);

  // TODO: Store in database
  if (db) {
    try {
      await storeInDatabase(auditEvent, db);
    } catch (error) {
      console.error("Failed to store audit event in database:", error);
    }
  }

  // Check for critical events and alert
  if (auditEvent.severity === "CRITICAL") {
    await handleCriticalEvent(auditEvent);
  }
}

/**
 * Generate unique event ID
 */
function generateEventId(): string {
  return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Log audit event to console with formatting
 */
function logToConsole(event: AuditEvent): void {
  const logLevel = getSeverityLogLevel(event.severity);
  const logMessage = formatLogMessage(event);

  console[logLevel](`[AUDIT] ${logMessage}`);
}

/**
 * Get console log level for severity
 */
function getSeverityLogLevel(severity: AuditSeverity): "log" | "warn" | "error" {
  switch (severity) {
    case "LOW":
    case "MEDIUM":
      return "log";
    case "HIGH":
      return "warn";
    case "CRITICAL":
      return "error";
    default:
      return "log";
  }
}

/**
 * Format audit event for logging
 */
function formatLogMessage(event: AuditEvent): string {
  const parts = [
    `[${event.severity}]`,
    `[${event.eventType}]`,
    `${event.action}`,
    `- IP: ${event.ipAddress}`,
    event.userUuid ? `- User: ${event.userUuid}` : "",
    `- ${event.method} ${event.endpoint}`,
    event.statusCode ? `- Status: ${event.statusCode}` : "",
    event.success ? "✓" : "✗",
  ].filter(Boolean);

  return parts.join(" ");
}

/**
 * Store audit event in database
 */
async function storeInDatabase(event: AuditEvent, db: Database): Promise<void> {
  // TODO: Implement database storage
  // This would typically insert into an audit_logs table
  console.log("TODO: Store audit event in database:", event.id);
}

/**
 * Handle critical security events
 */
async function handleCriticalEvent(event: AuditEvent): Promise<void> {
  console.error(`🚨 CRITICAL SECURITY EVENT: ${event.action}`, {
    eventId: event.id,
    userUuid: event.userUuid,
    ipAddress: event.ipAddress,
    endpoint: event.endpoint,
    details: event.details,
  });

  // TODO: Implement alerting (email, Slack, etc.)
  // TODO: Implement automatic blocking for severe threats
}

/**
 * Authentication event loggers
 */
export const authAudit = {
  loginSuccess: (userUuid: string, request: Request) =>
    logAuditEvent({
      eventType: "AUTHENTICATION",
      severity: "LOW",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "LOGIN_SUCCESS",
      success: true,
    }),

  loginFailure: (email: string, request: Request, reason: string) =>
    logAuditEvent({
      eventType: "AUTHENTICATION",
      severity: "MEDIUM",
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "LOGIN_FAILURE",
      success: false,
      errorMessage: reason,
      details: { email },
      riskScore: 3,
    }),

  logout: (userUuid: string, request: Request) =>
    logAuditEvent({
      eventType: "AUTHENTICATION",
      severity: "LOW",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "LOGOUT",
      success: true,
    }),

  passwordChange: (userUuid: string, request: Request) =>
    logAuditEvent({
      eventType: "AUTHENTICATION",
      severity: "MEDIUM",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "PASSWORD_CHANGE",
      success: true,
    }),
};

/**
 * Security violation event loggers
 */
export const securityAudit = {
  rateLimitExceeded: (request: Request, userUuid?: string) =>
    logAuditEvent({
      eventType: "RATE_LIMIT",
      severity: "MEDIUM",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "RATE_LIMIT_EXCEEDED",
      success: false,
      riskScore: 2,
    }),

  securityThreatDetected: (request: Request, threatType: string, userUuid?: string) =>
    logAuditEvent({
      eventType: "SECURITY_VIOLATION",
      severity: "HIGH",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "SECURITY_THREAT_DETECTED",
      success: false,
      details: { threatType },
      riskScore: 8,
    }),

  unauthorizedAccess: (request: Request, resource: string, userUuid?: string) =>
    logAuditEvent({
      eventType: "AUTHORIZATION",
      severity: "HIGH",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      resource,
      action: "UNAUTHORIZED_ACCESS",
      success: false,
      riskScore: 6,
    }),

  suspiciousActivity: (request: Request, activity: string, userUuid?: string) =>
    logAuditEvent({
      eventType: "SECURITY_VIOLATION",
      severity: "CRITICAL",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "SUSPICIOUS_ACTIVITY",
      success: false,
      details: { activity },
      riskScore: 9,
    }),
};

/**
 * API access event loggers
 */
export const apiAudit = {
  apiKeyUsed: (apiKeyId: string, request: Request, userUuid: string, success: boolean) =>
    logAuditEvent({
      eventType: "API_ACCESS",
      severity: "LOW",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "API_KEY_USED",
      success,
      details: { apiKeyId },
    }),

  invalidApiKey: (request: Request) =>
    logAuditEvent({
      eventType: "API_ACCESS",
      severity: "MEDIUM",
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      action: "INVALID_API_KEY",
      success: false,
      riskScore: 4,
    }),
};

/**
 * Data access event loggers
 */
export const dataAudit = {
  dataAccess: (userUuid: string, request: Request, resource: string, action: string) =>
    logAuditEvent({
      eventType: "DATA_ACCESS",
      severity: "LOW",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      resource,
      action: `DATA_${action.toUpperCase()}`,
      success: true,
    }),

  dataModification: (userUuid: string, request: Request, resource: string, action: string) =>
    logAuditEvent({
      eventType: "DATA_MODIFICATION",
      severity: "MEDIUM",
      userUuid,
      ipAddress: getClientIP(request),
      userAgent: request.headers.get("user-agent") || "unknown",
      endpoint: new URL(request.url).pathname,
      method: request.method,
      resource,
      action: `DATA_${action.toUpperCase()}`,
      success: true,
    }),
};

/**
 * Get client IP address
 */
function getClientIP(request: Request): string {
  const headers = request.headers;
  const ipHeaders = ["cf-connecting-ip", "x-forwarded-for", "x-real-ip", "x-client-ip"];

  for (const header of ipHeaders) {
    const value = headers.get(header);
    if (value) {
      return value.split(",")[0].trim();
    }
  }

  return "unknown";
}

/**
 * Get security metrics
 */
export function getSecurityMetrics(): SecurityMetrics {
  const now = Date.now();
  const oneHourAgo = now - 60 * 60 * 1000;

  const recentEvents = auditLogs.filter((event) => event.timestamp.getTime() > oneHourAgo);
  const failedLogins = auditLogs.filter(
    (event) => event.eventType === "AUTHENTICATION" && !event.success
  ).length;
  const securityViolations = auditLogs.filter(
    (event) => event.eventType === "SECURITY_VIOLATION"
  ).length;
  const rateLimitEvents = auditLogs.filter((event) => event.eventType === "RATE_LIMIT").length;

  // Calculate risky IPs
  const ipRiskMap = new Map<string, { riskScore: number; eventCount: number }>();
  auditLogs.forEach((event) => {
    if (event.riskScore && event.riskScore > 0) {
      const existing = ipRiskMap.get(event.ipAddress) || { riskScore: 0, eventCount: 0 };
      ipRiskMap.set(event.ipAddress, {
        riskScore: existing.riskScore + event.riskScore,
        eventCount: existing.eventCount + 1,
      });
    }
  });

  const topRiskyIPs = Array.from(ipRiskMap.entries())
    .map(([ip, data]) => ({ ip, ...data }))
    .sort((a, b) => b.riskScore - a.riskScore)
    .slice(0, 10);

  const recentCriticalEvents = auditLogs
    .filter((event) => event.severity === "CRITICAL")
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10);

  return {
    totalEvents: auditLogs.length,
    failedLogins,
    securityViolations,
    rateLimitExceeded: rateLimitEvents,
    suspiciousActivity: auditLogs.filter(
      (event) => event.action.includes("SUSPICIOUS") || (event.riskScore && event.riskScore >= 8)
    ).length,
    lastHourEvents: recentEvents.length,
    topRiskyIPs,
    recentCriticalEvents,
  };
}

/**
 * Get audit events with filtering
 */
export function getAuditEvents(
  options: {
    limit?: number;
    offset?: number;
    eventType?: AuditEventType;
    severity?: AuditSeverity;
    userUuid?: string;
    ipAddress?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): AuditEvent[] {
  let filtered = [...auditLogs];

  if (options.eventType) {
    filtered = filtered.filter((event) => event.eventType === options.eventType);
  }

  if (options.severity) {
    filtered = filtered.filter((event) => event.severity === options.severity);
  }

  if (options.userUuid) {
    filtered = filtered.filter((event) => event.userUuid === options.userUuid);
  }

  if (options.ipAddress) {
    filtered = filtered.filter((event) => event.ipAddress === options.ipAddress);
  }

  if (options.startDate) {
    filtered = filtered.filter((event) => event.timestamp >= options.startDate!);
  }

  if (options.endDate) {
    filtered = filtered.filter((event) => event.timestamp <= options.endDate!);
  }

  // Sort by timestamp (newest first)
  filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  // Apply pagination
  const offset = options.offset || 0;
  const limit = options.limit || 100;

  return filtered.slice(offset, offset + limit);
}

/**
 * Clear old audit logs (for memory management)
 */
export function clearOldAuditLogs(olderThanDays = 30): number {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

  const initialLength = auditLogs.length;
  const filtered = auditLogs.filter((event) => event.timestamp >= cutoffDate);

  auditLogs.length = 0;
  auditLogs.push(...filtered);

  const removedCount = initialLength - auditLogs.length;
  if (removedCount > 0) {
    console.log(`[AUDIT] Cleared ${removedCount} old audit logs`);
  }

  return removedCount;
}
