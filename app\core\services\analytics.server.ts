/**
 * Unified Analytics Service
 * Provides comprehensive analytics, usage tracking, and reporting for users and admins
 * Consolidates both analytics and usage tracking functionality
 */

import { and, asc, avg, count, desc, eq, gte, lte, sql, sum } from "drizzle-orm";
import type { Database } from "~/core/db/db";
import {
  apiUsage,
  creditTransactions,
  feedback,
  notifications,
  orders,
  rateLimits,
  subscriptions,
  usageStats,
  users,
} from "~/core/db/schema";

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

// Usage tracking interfaces
export interface UsageTrackingData {
  userUuid: string;
  endpoint: string;
  method: string;
  provider?: string;
  model?: string;
  requestSize?: number;
  responseSize?: number;
  tokensUsed?: number;
  creditsUsed?: number;
  duration: number;
  status: "success" | "error" | "timeout";
  errorCode?: string;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

export interface RateLimitConfig {
  endpoint: string;
  windowMinutes: number;
  maxRequests: number;
  maxTokens?: number;
  maxCredits?: number;
}

export interface UserAnalytics {
  overview: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    totalTokens: number;
    totalCredits: number;
    totalCost: number;
    avgResponseTime: number;
  };
  usage: {
    byProvider: Record<string, number>;
    byModel: Record<string, number>;
    byEndpoint: Record<string, number>;
    byStatus: Record<string, number>;
  };
  trends: {
    dailyUsage: Array<{
      date: string;
      requests: number;
      tokens: number;
      credits: number;
      cost: number;
    }>;
    hourlyDistribution: Array<{
      hour: number;
      requests: number;
    }>;
  };
  recentActivity: Array<{
    id: string;
    endpoint: string;
    method: string;
    provider?: string;
    model?: string;
    tokensUsed?: number;
    creditsUsed?: number;
    duration: number;
    status: string;
    createdAt: string;
  }>;
}

export interface AdminAnalytics {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    monthlyRevenue: number;
    totalOrders: number;
    totalSubscriptions: number;
    totalApiCalls: number;
    avgRevenuePerUser: number;
  };
  users: {
    newUsers: Array<{
      date: string;
      count: number;
    }>;
    userGrowth: {
      total: number;
      growth: number;
      growthRate: number;
    };
    topUsers: Array<{
      userUuid: string;
      userName: string;
      userEmail: string;
      totalRequests: number;
      totalCredits: number;
      totalSpent: number;
    }>;
  };
  revenue: {
    dailyRevenue: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
    revenueByPlan: Record<string, number>;
    subscriptionMetrics: {
      active: number;
      cancelled: number;
      churnRate: number;
    };
  };
  usage: {
    totalApiCalls: number;
    totalTokens: number;
    totalCredits: number;
    byProvider: Record<string, number>;
    byModel: Record<string, number>;
    topEndpoints: Array<{
      endpoint: string;
      requests: number;
      avgDuration: number;
    }>;
  };
  feedback: {
    totalFeedback: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    averageRating: number;
    recentFeedback: Array<{
      id: number;
      title: string;
      type: string;
      status: string;
      rating?: number;
      createdAt: string;
    }>;
  };
}

/**
 * Get user analytics for a specific date range
 */
export async function getUserAnalytics(
  userUuid: string,
  dateRange: DateRange,
  db: Database
): Promise<UserAnalytics> {
  try {
    const { startDate, endDate } = dateRange;

    // Overview metrics
    const overviewResult = await db
      .select({
        totalRequests: count(),
        successfulRequests: sum(sql`CASE WHEN status = 'success' THEN 1 ELSE 0 END`),
        failedRequests: sum(sql`CASE WHEN status != 'success' THEN 1 ELSE 0 END`),
        totalTokens: sum(apiUsage.tokensUsed),
        totalCredits: sum(apiUsage.creditsUsed),
        avgResponseTime: avg(apiUsage.duration),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      );

    const overview = overviewResult[0];
    const successRate =
      overview.totalRequests > 0
        ? (Number(overview.successfulRequests) / Number(overview.totalRequests)) * 100
        : 0;

    // Usage breakdown by provider
    const providerResults = await db
      .select({
        provider: apiUsage.provider,
        requests: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.provider);

    const byProvider: Record<string, number> = {};
    providerResults.forEach((result) => {
      if (result.provider) {
        byProvider[result.provider] = result.requests;
      }
    });

    // Usage breakdown by model
    const modelResults = await db
      .select({
        model: apiUsage.model,
        requests: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.model);

    const byModel: Record<string, number> = {};
    modelResults.forEach((result) => {
      if (result.model) {
        byModel[result.model] = result.requests;
      }
    });

    // Usage breakdown by endpoint
    const endpointResults = await db
      .select({
        endpoint: apiUsage.endpoint,
        requests: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.endpoint);

    const byEndpoint: Record<string, number> = {};
    endpointResults.forEach((result) => {
      byEndpoint[result.endpoint] = result.requests;
    });

    // Usage breakdown by status
    const statusResults = await db
      .select({
        status: apiUsage.status,
        requests: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.status);

    const byStatus: Record<string, number> = {};
    statusResults.forEach((result) => {
      byStatus[result.status] = result.requests;
    });

    // Daily usage trends
    const dailyResults = await db
      .select({
        date: sql<string>`DATE(${apiUsage.createdAt})`,
        requests: count(),
        tokens: sum(apiUsage.tokensUsed),
        credits: sum(apiUsage.creditsUsed),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(sql`DATE(${apiUsage.createdAt})`)
      .orderBy(sql`DATE(${apiUsage.createdAt})`);

    const dailyUsage = dailyResults.map((result) => ({
      date: result.date,
      requests: result.requests,
      tokens: Number(result.tokens) || 0,
      credits: Number(result.credits) || 0,
      cost: (Number(result.credits) || 0) * 0.01, // Assuming 1 credit = $0.01
    }));

    // Hourly distribution
    const hourlyResults = await db
      .select({
        hour: sql<number>`EXTRACT(HOUR FROM ${apiUsage.createdAt})`,
        requests: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(sql`EXTRACT(HOUR FROM ${apiUsage.createdAt})`)
      .orderBy(sql`EXTRACT(HOUR FROM ${apiUsage.createdAt})`);

    const hourlyDistribution = hourlyResults.map((result) => ({
      hour: result.hour,
      requests: result.requests,
    }));

    // Recent activity
    const recentResults = await db
      .select()
      .from(apiUsage)
      .where(eq(apiUsage.userUuid, userUuid))
      .orderBy(desc(apiUsage.createdAt))
      .limit(20);

    const recentActivity = recentResults.map((result) => ({
      id: result.id,
      endpoint: result.endpoint,
      method: result.method,
      provider: result.provider,
      model: result.model,
      tokensUsed: result.tokensUsed,
      creditsUsed: result.creditsUsed,
      duration: result.duration,
      status: result.status,
      createdAt: result.createdAt.toISOString(),
    }));

    return {
      overview: {
        totalRequests: Number(overview.totalRequests) || 0,
        successfulRequests: Number(overview.successfulRequests) || 0,
        failedRequests: Number(overview.failedRequests) || 0,
        successRate: Math.round(successRate * 100) / 100,
        totalTokens: Number(overview.totalTokens) || 0,
        totalCredits: Number(overview.totalCredits) || 0,
        totalCost: (Number(overview.totalCredits) || 0) * 0.01,
        avgResponseTime: Math.round(Number(overview.avgResponseTime) || 0),
      },
      usage: {
        byProvider,
        byModel,
        byEndpoint,
        byStatus,
      },
      trends: {
        dailyUsage,
        hourlyDistribution,
      },
      recentActivity,
    };
  } catch (error) {
    console.error("Error getting user analytics:", error);
    throw new Error("Failed to get user analytics");
  }
}

/**
 * Get simplified user analytics summary for dashboard
 */
export async function getUserAnalyticsSummary(
  userUuid: string,
  dateRange: DateRange,
  db: Database
): Promise<{
  totalRequests: number;
  successRate: number;
  totalCredits: number;
  totalCost: number;
  avgResponseTime: number;
  topProvider: string | null;
}> {
  try {
    const analytics = await getUserAnalytics(userUuid, dateRange, db);

    const topProvider =
      Object.entries(analytics.usage.byProvider).sort(([, a], [, b]) => b - a)[0]?.[0] || null;

    return {
      totalRequests: analytics.overview.totalRequests,
      successRate: analytics.overview.successRate,
      totalCredits: analytics.overview.totalCredits,
      totalCost: analytics.overview.totalCost,
      avgResponseTime: analytics.overview.avgResponseTime,
      topProvider,
    };
  } catch (error) {
    console.error("Error getting user analytics summary:", error);
    throw new Error("Failed to get user analytics summary");
  }
}

/**
 * Get admin analytics for system overview
 */
export async function getAdminAnalytics(
  dateRange: DateRange,
  db: Database
): Promise<AdminAnalytics> {
  try {
    const { startDate, endDate } = dateRange;

    // Overview metrics
    const [
      totalUsersResult,
      activeUsersResult,
      revenueResult,
      ordersResult,
      subscriptionsResult,
      apiCallsResult,
    ] = await Promise.all([
      // Total users
      db
        .select({ count: count() })
        .from(users),

      // Active users (users with API calls in date range)
      db
        .select({ count: count(sql`DISTINCT ${apiUsage.userUuid}`) })
        .from(apiUsage)
        .where(and(gte(apiUsage.createdAt, startDate), lte(apiUsage.createdAt, endDate))),

      // Revenue metrics
      db
        .select({
          totalRevenue: sum(orders.totalAmount),
          monthlyRevenue: sum(
            sql`CASE WHEN ${orders.createdAt} >= ${sql`NOW() - INTERVAL '30 days'`} THEN ${orders.totalAmount} ELSE 0 END`
          ),
        })
        .from(orders),

      // Orders count
      db
        .select({ count: count() })
        .from(orders),

      // Subscriptions count
      db
        .select({ count: count() })
        .from(subscriptions),

      // API calls count
      db
        .select({ count: count() })
        .from(apiUsage)
        .where(and(gte(apiUsage.createdAt, startDate), lte(apiUsage.createdAt, endDate))),
    ]);

    const totalUsers = totalUsersResult[0]?.count || 0;
    const activeUsers = activeUsersResult[0]?.count || 0;
    const totalRevenue = Number(revenueResult[0]?.totalRevenue) || 0;
    const monthlyRevenue = Number(revenueResult[0]?.monthlyRevenue) || 0;
    const totalOrders = ordersResult[0]?.count || 0;
    const totalSubscriptions = subscriptionsResult[0]?.count || 0;
    const totalApiCalls = apiCallsResult[0]?.count || 0;
    const avgRevenuePerUser = totalUsers > 0 ? totalRevenue / totalUsers : 0;

    // New users trend
    const newUsersResults = await db
      .select({
        date: sql<string>`DATE(${users.createdAt})`,
        count: count(),
      })
      .from(users)
      .where(and(gte(users.createdAt, startDate), lte(users.createdAt, endDate)))
      .groupBy(sql`DATE(${users.createdAt})`)
      .orderBy(sql`DATE(${users.createdAt})`);

    const newUsers = newUsersResults.map((result) => ({
      date: result.date,
      count: result.count,
    }));

    // Top users by usage
    const topUsersResults = await db
      .select({
        userUuid: apiUsage.userUuid,
        userName: users.name,
        userEmail: users.email,
        totalRequests: count(),
        totalCredits: sum(apiUsage.creditsUsed),
      })
      .from(apiUsage)
      .leftJoin(users, eq(apiUsage.userUuid, users.uuid))
      .where(and(gte(apiUsage.createdAt, startDate), lte(apiUsage.createdAt, endDate)))
      .groupBy(apiUsage.userUuid, users.name, users.email)
      .orderBy(desc(count()))
      .limit(10);

    const topUsers = topUsersResults.map((result) => ({
      userUuid: result.userUuid,
      userName: result.userName || "Unknown",
      userEmail: result.userEmail || "Unknown",
      totalRequests: result.totalRequests,
      totalCredits: Number(result.totalCredits) || 0,
      totalSpent: (Number(result.totalCredits) || 0) * 0.01,
    }));

    // Daily revenue
    const dailyRevenueResults = await db
      .select({
        date: sql<string>`DATE(${orders.createdAt})`,
        revenue: sum(orders.totalAmount),
        orders: count(),
      })
      .from(orders)
      .where(and(gte(orders.createdAt, startDate), lte(orders.createdAt, endDate)))
      .groupBy(sql`DATE(${orders.createdAt})`)
      .orderBy(sql`DATE(${orders.createdAt})`);

    const dailyRevenue = dailyRevenueResults.map((result) => ({
      date: result.date,
      revenue: Number(result.revenue) || 0,
      orders: result.orders,
    }));

    // Usage by provider
    const providerResults = await db
      .select({
        provider: apiUsage.provider,
        requests: count(),
      })
      .from(apiUsage)
      .where(and(gte(apiUsage.createdAt, startDate), lte(apiUsage.createdAt, endDate)))
      .groupBy(apiUsage.provider);

    const usageByProvider: Record<string, number> = {};
    providerResults.forEach((result) => {
      if (result.provider) {
        usageByProvider[result.provider] = result.requests;
      }
    });

    // Top endpoints
    const endpointResults = await db
      .select({
        endpoint: apiUsage.endpoint,
        requests: count(),
        avgDuration: avg(apiUsage.duration),
      })
      .from(apiUsage)
      .where(and(gte(apiUsage.createdAt, startDate), lte(apiUsage.createdAt, endDate)))
      .groupBy(apiUsage.endpoint)
      .orderBy(desc(count()))
      .limit(10);

    const topEndpoints = endpointResults.map((result) => ({
      endpoint: result.endpoint,
      requests: result.requests,
      avgDuration: Math.round(Number(result.avgDuration) || 0),
    }));

    // Feedback metrics
    const feedbackOverview = await db
      .select({
        totalFeedback: count(),
        averageRating: avg(feedback.rating),
      })
      .from(feedback);

    const feedbackByType = await db
      .select({
        type: feedback.type,
        count: count(),
      })
      .from(feedback)
      .groupBy(feedback.type);

    const feedbackByStatus = await db
      .select({
        status: feedback.status,
        count: count(),
      })
      .from(feedback)
      .groupBy(feedback.status);

    const recentFeedbackResults = await db
      .select({
        id: feedback.id,
        title: feedback.title,
        type: feedback.type,
        status: feedback.status,
        rating: feedback.rating,
        createdAt: feedback.createdAt,
      })
      .from(feedback)
      .orderBy(desc(feedback.createdAt))
      .limit(10);

    return {
      overview: {
        totalUsers,
        activeUsers,
        totalRevenue,
        monthlyRevenue,
        totalOrders,
        totalSubscriptions,
        totalApiCalls,
        avgRevenuePerUser: Math.round(avgRevenuePerUser * 100) / 100,
      },
      users: {
        newUsers,
        userGrowth: {
          total: totalUsers,
          growth: newUsers.reduce((sum, day) => sum + day.count, 0),
          growthRate: 0, // TODO: Calculate growth rate
        },
        topUsers,
      },
      revenue: {
        dailyRevenue,
        revenueByPlan: {}, // TODO: Implement
        subscriptionMetrics: {
          active: totalSubscriptions,
          cancelled: 0, // TODO: Implement
          churnRate: 0, // TODO: Implement
        },
      },
      usage: {
        totalApiCalls,
        totalTokens: 0, // TODO: Implement
        totalCredits: 0, // TODO: Implement
        byProvider: usageByProvider,
        byModel: {}, // TODO: Implement
        topEndpoints,
      },
      feedback: {
        totalFeedback: feedbackOverview[0]?.totalFeedback || 0,
        byType: Object.fromEntries(feedbackByType.map((f) => [f.type, f.count])),
        byStatus: Object.fromEntries(feedbackByStatus.map((f) => [f.status, f.count])),
        averageRating: Math.round((Number(feedbackOverview[0]?.averageRating) || 0) * 100) / 100,
        recentFeedback: recentFeedbackResults.map((f) => ({
          id: f.id,
          title: f.title,
          type: f.type,
          status: f.status,
          rating: f.rating,
          createdAt: f.createdAt.toISOString(),
        })),
      },
    };
  } catch (error) {
    console.error("Error getting admin analytics:", error);
    throw new Error("Failed to get admin analytics");
  }
}

/**
 * Track API usage
 */
export async function trackApiUsage(
  data: UsageTrackingData,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db.insert(apiUsage).values({
      userUuid: data.userUuid,
      endpoint: data.endpoint,
      method: data.method,
      provider: data.provider,
      model: data.model,
      requestSize: data.requestSize,
      responseSize: data.responseSize,
      tokensUsed: data.tokensUsed,
      creditsUsed: data.creditsUsed,
      duration: data.duration,
      status: data.status,
      errorCode: data.errorCode,
      errorMessage: data.errorMessage,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: data.metadata,
      createdAt: new Date(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error tracking API usage:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Check rate limits for a user and endpoint
 */
export async function checkRateLimit(
  userUuid: string,
  endpoint: string,
  config: RateLimitConfig,
  db: Database
): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  current: {
    requests: number;
    tokens: number;
    credits: number;
  };
}> {
  try {
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.windowMinutes * 60 * 1000);

    // Get current usage in the window
    const currentUsage = await db
      .select({
        requests: count(),
        tokens: sum(apiUsage.tokensUsed),
        credits: sum(apiUsage.creditsUsed),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          eq(apiUsage.endpoint, endpoint),
          gte(apiUsage.createdAt, windowStart)
        )
      );

    const usage = currentUsage[0];
    const currentRequests = Number(usage.requests) || 0;
    const currentTokens = Number(usage.tokens) || 0;
    const currentCredits = Number(usage.credits) || 0;

    // Check limits
    const requestsAllowed = currentRequests < config.maxRequests;
    const tokensAllowed = !config.maxTokens || currentTokens < config.maxTokens;
    const creditsAllowed = !config.maxCredits || currentCredits < config.maxCredits;

    const allowed = requestsAllowed && tokensAllowed && creditsAllowed;
    const remaining = Math.max(0, config.maxRequests - currentRequests);
    const resetTime = new Date(windowStart.getTime() + config.windowMinutes * 60 * 1000);

    return {
      allowed,
      remaining,
      resetTime,
      current: {
        requests: currentRequests,
        tokens: currentTokens,
        credits: currentCredits,
      },
    };
  } catch (error) {
    console.error("Error checking rate limit:", error);
    // Allow request on error to avoid blocking users
    return {
      allowed: true,
      remaining: config.maxRequests,
      resetTime: new Date(Date.now() + config.windowMinutes * 60 * 1000),
      current: {
        requests: 0,
        tokens: 0,
        credits: 0,
      },
    };
  }
}

/**
 * Get recent API usage for a user
 */
export async function getRecentUsage(
  userUuid: string,
  limit = 50,
  db: Database
): Promise<
  Array<{
    id: string;
    endpoint: string;
    method: string;
    provider?: string;
    model?: string;
    tokensUsed?: number;
    creditsUsed?: number;
    duration: number;
    status: string;
    createdAt: string;
  }>
> {
  try {
    const usage = await db
      .select({
        id: apiUsage.id,
        endpoint: apiUsage.endpoint,
        method: apiUsage.method,
        provider: apiUsage.provider,
        model: apiUsage.model,
        tokensUsed: apiUsage.tokensUsed,
        creditsUsed: apiUsage.creditsUsed,
        duration: apiUsage.duration,
        status: apiUsage.status,
        createdAt: apiUsage.createdAt,
      })
      .from(apiUsage)
      .where(eq(apiUsage.userUuid, userUuid))
      .orderBy(desc(apiUsage.createdAt))
      .limit(limit);

    return usage.map((item) => ({
      id: item.id,
      endpoint: item.endpoint,
      method: item.method,
      provider: item.provider || undefined,
      model: item.model || undefined,
      tokensUsed: item.tokensUsed || undefined,
      creditsUsed: item.creditsUsed || undefined,
      duration: item.duration,
      status: item.status,
      createdAt: item.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting recent usage:", error);
    return [];
  }
}

/**
 * Calculate cost based on usage and provider pricing
 */
export function calculateUsageCost(
  provider: string,
  model: string,
  tokensUsed: number,
  operation: "text" | "image" | "embedding" = "text"
): number {
  // Simplified pricing model - in production, this would be more sophisticated
  const pricing: Record<string, Record<string, number>> = {
    openai: {
      "gpt-4o": 0.00003, // per token
      "gpt-4o-mini": 0.000001,
      "gpt-3.5-turbo": 0.000001,
      "dall-e-3": 0.04, // per image
      "dall-e-2": 0.02,
    },
    deepseek: {
      "deepseek-chat": 0.000001,
      "deepseek-coder": 0.000001,
    },
    cloudflare: {
      "llama-3.2-3b": 0.0000005,
      "llama-3-8b": 0.000001,
    },
  };

  const providerPricing = pricing[provider];
  if (!providerPricing) return 0;

  const modelPrice = providerPricing[model];
  if (!modelPrice) return 0;

  if (operation === "image") {
    return modelPrice; // Fixed price per image
  }

  return tokensUsed * modelPrice;
}

/**
 * Generate usage summary for a period
 */
export async function generateUsageSummary(
  userUuid: string,
  period: "daily" | "weekly" | "monthly",
  date: Date,
  db: Database
): Promise<{
  success: boolean;
  summary?: {
    period: string;
    periodStart: Date;
    periodEnd: Date;
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalTokens: number;
    totalCredits: number;
    totalCost: number;
    avgResponseTime: number;
    topProvider?: string;
    topModel?: string;
    topEndpoint?: string;
  };
  error?: string;
}> {
  try {
    let periodStart: Date;
    let periodEnd: Date;

    // Calculate period boundaries
    switch (period) {
      case "daily":
        periodStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);
        break;
      case "weekly": {
        const dayOfWeek = date.getDay();
        periodStart = new Date(date.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
        periodStart = new Date(
          periodStart.getFullYear(),
          periodStart.getMonth(),
          periodStart.getDate()
        );
        periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      }
      case "monthly":
        periodStart = new Date(date.getFullYear(), date.getMonth(), 1);
        periodEnd = new Date(date.getFullYear(), date.getMonth() + 1, 1);
        break;
    }

    const analytics = await getUserAnalytics(
      userUuid,
      { startDate: periodStart, endDate: periodEnd },
      db
    );

    const summary = {
      period,
      periodStart,
      periodEnd,
      totalRequests: analytics.overview.totalRequests,
      successfulRequests: analytics.overview.successfulRequests,
      failedRequests: analytics.overview.failedRequests,
      totalTokens: analytics.overview.totalTokens,
      totalCredits: analytics.overview.totalCredits,
      totalCost: analytics.overview.totalCost,
      avgResponseTime: analytics.overview.avgResponseTime,
      topProvider: Object.entries(analytics.usage.byProvider).sort(([, a], [, b]) => b - a)[0]?.[0],
      topModel: Object.entries(analytics.usage.byModel).sort(([, a], [, b]) => b - a)[0]?.[0],
      topEndpoint: Object.entries(analytics.usage.byEndpoint).sort(([, a], [, b]) => b - a)[0]?.[0],
    };

    return { success: true, summary };
  } catch (error) {
    console.error("Error generating usage summary:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Backward compatibility exports for usage-tracking.server.ts
export const getUserUsageAnalytics = getUserAnalytics;
export type UsageAnalytics = UserAnalytics;
