/**
 * User model and database operations
 * Updated to use new database operations and queries
 */

import { eq } from "drizzle-orm";
import { getUuid } from "~/core/auth/hash";
import { dbOperations, dbQueries, users } from "~/core/db";
import type { Database } from "~/core/db/db";
import type { User as DbUser, NewUser } from "~/core/db/schema";

// Legacy interface for backward compatibility
export interface User {
  id?: string;
  uuid: string;
  name: string;
  email: string;
  avatar?: string;
  credits?: number;
  invite_code?: string;
  invited_by?: string;
  created_at: string;
  updated_at: string;
}

// Convert database user to legacy format
function convertDbUserToLegacy(dbUser: DbUser): User {
  return {
    id: dbUser.id,
    uuid: dbUser.uuid,
    name: dbUser.name,
    email: dbUser.email,
    avatar: dbUser.avatar || undefined,
    credits: dbUser.credits || 0,
    invite_code: dbUser.inviteCode || undefined,
    invited_by: dbUser.invitedBy || undefined,
    created_at: dbUser.createdAt.toISOString(),
    updated_at: dbUser.updatedAt.toISOString(),
  };
}

/**
 * Find user by UUID
 * Updated to use new database operations
 */
export async function findUserByUuid(uuid: string, db: Database): Promise<User | null> {
  try {
    const dbUser = await dbOperations.user.findByUuid(db, uuid);
    return dbUser ? convertDbUserToLegacy(dbUser) : null;
  } catch (error) {
    console.error("Error finding user by UUID:", error);
    return null;
  }
}

/**
 * Find user by email
 * Updated to use new database operations
 */
export async function findUserByEmail(email: string, db: Database): Promise<User | null> {
  try {
    const dbUser = await dbOperations.user.findByEmail(db, email);
    return dbUser ? convertDbUserToLegacy(dbUser) : null;
  } catch (error) {
    console.error("Error finding user by email:", error);
    return null;
  }
}

/**
 * Find user by invite code
 * Note: This function needs to be implemented in dbOperations
 */
export async function findUserByInviteCode(inviteCode: string, db: Database): Promise<User | null> {
  try {
    const result = await db.select().from(users).where(eq(users.inviteCode, inviteCode)).limit(1);

    if (result.length === 0) {
      return null;
    }

    return convertDbUserToLegacy(result[0]);
  } catch (error) {
    console.error("Error finding user by invite code:", error);
    return null;
  }
}

/**
 * Update user invite code
 */
export async function updateUserInviteCode(
  uuid: string,
  inviteCode: string,
  db: Database
): Promise<boolean> {
  try {
    await db
      .update(users)
      .set({
        inviteCode: inviteCode,
        updatedAt: new Date(),
      })
      .where(eq(users.uuid, uuid));

    return true;
  } catch (error) {
    console.error("Error updating user invite code:", error);
    return false;
  }
}

/**
 * Update user invited_by field
 */
export async function updateUserInvitedBy(
  uuid: string,
  invitedBy: string,
  db: Database
): Promise<boolean> {
  try {
    await db
      .update(users)
      .set({
        invitedBy: invitedBy,
        updatedAt: new Date(),
      })
      .where(eq(users.uuid, uuid));

    return true;
  } catch (error) {
    console.error("Error updating user invited_by:", error);
    return false;
  }
}

/**
 * Create new user
 */
export async function createUser(
  user: Omit<User, "id" | "created_at" | "updated_at">,
  db: Database
): Promise<User> {
  try {
    const now = new Date();
    const result = await db
      .insert(users)
      .values({
        id: getUuid(),
        uuid: user.uuid,
        name: user.name,
        email: user.email,
        avatar: user.avatar || null,
        credits: user.credits || 0,
        inviteCode: user.invite_code || null,
        invitedBy: user.invited_by || null,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    const newUser = result[0];
    return {
      id: newUser.id,
      uuid: newUser.uuid,
      name: newUser.name,
      email: newUser.email,
      avatar: newUser.avatar || undefined,
      credits: newUser.credits || 0,
      invite_code: newUser.inviteCode || undefined,
      invited_by: newUser.invitedBy || undefined,
      created_at: newUser.createdAt.toISOString(),
      updated_at: newUser.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error("Error creating user:", error);
    throw error;
  }
}

/**
 * Update user credits
 * Updated to use new database operations with transaction support
 */
export async function updateUserCredits(
  uuid: string,
  credits: number,
  db: Database
): Promise<boolean> {
  try {
    // Calculate the credit change
    const currentUser = await dbOperations.user.findByUuid(db, uuid);
    if (!currentUser) {
      throw new Error("User not found");
    }

    const creditChange = credits - (currentUser.credits || 0);

    // Use the new credit update operation with transaction logging
    await dbOperations.user.updateCredits(
      db,
      uuid,
      creditChange,
      "manual_update",
      `Credits updated to ${credits}`
    );

    return true;
  } catch (error) {
    console.error("Error updating user credits:", error);
    return false;
  }
}

// New enhanced functions using the new database operations

/**
 * Get user with all related data (orders, credits, etc.)
 */
export async function getUserWithRelations(uuid: string, db: Database) {
  try {
    const user = await dbOperations.user.findByUuid(db, uuid);
    if (!user) return null;

    return await dbQueries.user.getUserWithRelations(db, user.id);
  } catch (error) {
    console.error("Error getting user with relations:", error);
    return null;
  }
}

/**
 * Search users with advanced filters
 */
export async function searchUsers(
  db: Database,
  options: {
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    isAffiliate?: boolean;
    minCredits?: number;
    maxCredits?: number;
  } = {}
) {
  try {
    return await dbQueries.user.searchUsers(db, options);
  } catch (error) {
    console.error("Error searching users:", error);
    throw error;
  }
}

/**
 * Get user credit balance
 */
export async function getUserCreditBalance(uuid: string, db: Database): Promise<number> {
  try {
    return await dbOperations.credit.getUserCreditBalance(db, uuid);
  } catch (error) {
    console.error("Error getting user credit balance:", error);
    return 0;
  }
}

/**
 * Get user credit history with pagination
 */
export async function getUserCreditHistory(
  uuid: string,
  db: Database,
  options: { page?: number; limit?: number } = {}
) {
  try {
    return await dbOperations.credit.getUserCreditHistory(db, uuid, options);
  } catch (error) {
    console.error("Error getting user credit history:", error);
    throw error;
  }
}

/**
 * Update user profile information
 */
export async function updateUser(
  uuid: string,
  updates: Partial<{
    name: string;
    email: string;
    avatar: string | null;
    inviteCode: string;
  }>,
  db: Database
): Promise<boolean> {
  try {
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (updates.name !== undefined) {
      updateData.name = updates.name;
    }
    if (updates.email !== undefined) {
      updateData.email = updates.email;
    }
    if (updates.avatar !== undefined) {
      updateData.avatar = updates.avatar;
    }
    if (updates.inviteCode !== undefined) {
      updateData.inviteCode = updates.inviteCode;
    }

    await db.update(users).set(updateData).where(eq(users.uuid, uuid));

    return true;
  } catch (error) {
    console.error("Error updating user:", error);
    return false;
  }
}
