/**
 * Common Style Constants
 * Centralized Tailwind CSS class combinations to reduce duplication
 */

// Layout patterns
export const layouts = {
  // Flexbox patterns
  flexCenter: "flex items-center justify-center",
  flexBetween: "flex items-center justify-between",
  flexStart: "flex items-center justify-start",
  flexEnd: "flex items-center justify-end",
  flexCol: "flex flex-col",
  flexColCenter: "flex flex-col items-center justify-center",
  flexWrap: "flex flex-wrap",

  // Grid patterns
  gridCols2: "grid grid-cols-1 md:grid-cols-2",
  gridCols3: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  gridCols4: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  gridGap: "gap-4",
  gridGapLg: "gap-6",

  // Container patterns
  container: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  containerSm: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",

  // Full height patterns
  fullHeight: "min-h-screen",
  fullHeightFlex: "min-h-screen flex flex-col",
} as const;

// Background patterns
export const backgrounds = {
  // Primary backgrounds
  page: "bg-gray-50 dark:bg-gray-900",
  card: "bg-white dark:bg-gray-900",
  cardElevated: "bg-white dark:bg-gray-800 shadow-sm",
  cardWithBorder: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800",

  // Interactive backgrounds
  hover: "hover:bg-gray-50 dark:hover:bg-gray-800",
  hoverCard: "hover:bg-gray-100 dark:hover:bg-gray-700",
  active: "bg-blue-50 dark:bg-blue-900/20",

  // Status backgrounds
  success: "bg-green-50 dark:bg-green-900/20",
  warning: "bg-yellow-50 dark:bg-yellow-900/20",
  error: "bg-red-50 dark:bg-red-900/20",
  info: "bg-blue-50 dark:bg-blue-900/20",
} as const;

// Text patterns
export const text = {
  // Hierarchy
  heading1: "text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-4xl",
  heading2: "text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-3xl",
  heading3: "text-xl font-semibold text-gray-900 dark:text-gray-100",
  heading4: "text-lg font-semibold text-gray-900 dark:text-gray-100",

  // Body text
  body: "text-gray-900 dark:text-gray-100",
  bodyMuted: "text-gray-600 dark:text-gray-400",
  bodySmall: "text-sm text-gray-600 dark:text-gray-400",
  bodyLarge: "text-lg text-gray-900 dark:text-gray-100",

  // Special text
  caption: "text-xs text-gray-500 dark:text-gray-500",
  link: "text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline",
  linkMuted: "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100",

  // Status text colors
  success: "text-green-600 dark:text-green-400",
  warning: "text-yellow-600 dark:text-yellow-400",
  error: "text-red-600 dark:text-red-400",
  info: "text-blue-600 dark:text-blue-400",
} as const;

// Spacing patterns
export const spacing = {
  // Padding patterns
  section: "px-6 py-4",
  sectionLg: "px-8 py-6",
  card: "p-6",
  cardSm: "p-4",
  cardLg: "p-8",

  // Margin patterns
  sectionMargin: "my-8",
  cardMargin: "mb-6",
  elementMargin: "mb-4",

  // Gap patterns
  gapSm: "gap-2",
  gap: "gap-4",
  gapLg: "gap-6",
  gapXl: "gap-8",
} as const;

// Border patterns
export const borders = {
  // Basic borders
  default: "border border-gray-200 dark:border-gray-800",
  subtle: "border border-gray-100 dark:border-gray-900",
  emphasis: "border-2 border-gray-300 dark:border-gray-700",

  // Rounded corners
  rounded: "rounded-lg",
  roundedSm: "rounded-md",
  roundedXl: "rounded-xl",
  roundedFull: "rounded-full",

  // Dividers
  dividerX: "border-b border-gray-200 dark:border-gray-800",
  dividerY: "border-r border-gray-200 dark:border-gray-800",

  // Focus states
  focus: "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
  focusWithin: "focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent",
} as const;

// Shadow patterns
export const shadows = {
  card: "shadow-sm",
  cardHover: "shadow-md",
  modal: "shadow-xl",
  dropdown: "shadow-lg",
  none: "shadow-none",
} as const;

// Animation patterns
export const animations = {
  // Transitions
  default: "transition-all duration-200 ease-in-out",
  fast: "transition-all duration-150 ease-in-out",
  slow: "transition-all duration-300 ease-in-out",

  // Transform patterns
  scaleHover: "hover:scale-105 transition-transform duration-200",
  scalePress: "active:scale-95 transition-transform duration-100",

  // Fade patterns
  fadeIn: "animate-in fade-in duration-200",
  fadeOut: "animate-out fade-out duration-200",

  // Slide patterns
  slideInFromTop: "animate-in slide-in-from-top-2 duration-200",
  slideInFromBottom: "animate-in slide-in-from-bottom-2 duration-200",
  slideInFromLeft: "animate-in slide-in-from-left-2 duration-200",
  slideInFromRight: "animate-in slide-in-from-right-2 duration-200",
} as const;

// Interactive states
export const interactive = {
  // Button-like interactions
  clickable: "cursor-pointer select-none",
  clickableCard: "cursor-pointer select-none hover:shadow-md transition-shadow duration-200",

  // Disabled states
  disabled: "opacity-50 cursor-not-allowed",
  disabledCard: "opacity-50 cursor-not-allowed grayscale",

  // Loading states
  loading: "animate-pulse pointer-events-none",
  skeleton: "bg-gray-200 dark:bg-gray-800 animate-pulse rounded",
} as const;

// Form patterns
export const forms = {
  // Input patterns
  input:
    "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800",
  inputError: "border-red-500 focus:ring-red-500",
  inputSuccess: "border-green-500 focus:ring-green-500",

  // Label patterns
  label: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
  labelRequired:
    "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 after:content-['*'] after:ml-1 after:text-red-500",

  // Error patterns
  error: "text-sm text-red-600 dark:text-red-400 mt-1",
  help: "text-sm text-gray-500 dark:text-gray-400 mt-1",
} as const;

// Status patterns
export const status = {
  // Badge patterns
  successBadge: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
  warningBadge: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
  errorBadge: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
  infoBadge: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",

  // Indicator dots
  successDot: "h-2 w-2 bg-green-500 rounded-full",
  warningDot: "h-2 w-2 bg-yellow-500 rounded-full",
  errorDot: "h-2 w-2 bg-red-500 rounded-full",
  infoDot: "h-2 w-2 bg-blue-500 rounded-full",
} as const;

// Responsive patterns
export const responsive = {
  // Hide/show patterns
  hideMobile: "hidden sm:block",
  hideDesktop: "block sm:hidden",
  showMobile: "block sm:hidden",
  showDesktop: "hidden sm:block",

  // Stack patterns
  stackMobile: "flex flex-col sm:flex-row",
  stackDesktop: "flex flex-row sm:flex-col",

  // Text responsive
  textResponsive: "text-sm sm:text-base",
  textLargeResponsive: "text-base sm:text-lg lg:text-xl",
} as const;

// Utility function to combine multiple style patterns
export function combineStyles(...styles: string[]) {
  return styles.filter(Boolean).join(" ");
}

// Helper to get all values from a style category
export function getStyleValues<T extends Record<string, string>>(category: T): string[] {
  return Object.values(category);
}

// Type helpers for autocomplete
export type LayoutStyle = keyof typeof layouts;
export type BackgroundStyle = keyof typeof backgrounds;
export type TextStyle = keyof typeof text;
export type SpacingStyle = keyof typeof spacing;
export type BorderStyle = keyof typeof borders;
export type ShadowStyle = keyof typeof shadows;
export type AnimationStyle = keyof typeof animations;
export type InteractiveStyle = keyof typeof interactive;
export type FormStyle = keyof typeof forms;
export type StatusStyle = keyof typeof status;
export type ResponsiveStyle = keyof typeof responsive;
