/**
 * Database Query Optimization Utilities
 *
 * Provides tools for optimizing database queries, including:
 * - Query caching
 * - Query analysis
 * - Performance monitoring
 * - Batch operations
 */

import { CacheKeys, cache, cached } from "../cache";
import { NeonQueryMonitor } from "../neon/optimization";
import type { Database } from "./db";

export interface QueryOptions {
  cache?: boolean;
  cacheTtl?: number;
  timeout?: number;
  retries?: number;
  batchSize?: number;
}

export interface QueryResult<T = any> {
  data: T;
  metadata: {
    executionTime: number;
    fromCache: boolean;
    queryHash: string;
    timestamp: Date;
  };
}

export interface BatchOperation<T = any> {
  query: string;
  params: any[];
  expectedResult?: T;
}

/**
 * Query optimizer class for database operations
 */
export class QueryOptimizer {
  private db: Database;
  private defaultOptions: QueryOptions = {
    cache: true,
    cacheTtl: 300, // 5 minutes
    timeout: 30000, // 30 seconds
    retries: 2,
    batchSize: 100,
  };

  constructor(db: Database) {
    this.db = db;
  }

  /**
   * Execute a query with optimization features
   */
  async execute<T = any>(
    query: string,
    params: any[] = [],
    options: QueryOptions = {}
  ): Promise<QueryResult<T>> {
    const opts = { ...this.defaultOptions, ...options };
    const queryHash = this.generateQueryHash(query, params);
    const cacheKey = CacheKeys.dbQuery(query, JSON.stringify(params));

    // Try cache first if enabled
    if (opts.cache) {
      const cached = await cache.get<T>(cacheKey);
      if (cached !== null) {
        return {
          data: cached,
          metadata: {
            executionTime: 0,
            fromCache: true,
            queryHash,
            timestamp: new Date(),
          },
        };
      }
    }

    // Execute query with monitoring
    const monitor = NeonQueryMonitor.startQuery(query, params);
    const startTime = Date.now();

    try {
      const result = await this.executeWithRetry(query, params, opts);
      const executionTime = monitor.end();

      // Cache result if enabled
      if (opts.cache && result) {
        await cache.set(cacheKey, result, opts.cacheTtl);
      }

      return {
        data: result,
        metadata: {
          executionTime,
          fromCache: false,
          queryHash,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      monitor.end();
      throw error;
    }
  }

  /**
   * Execute multiple queries in a batch
   */
  async executeBatch<T = any>(
    operations: BatchOperation<T>[],
    options: QueryOptions = {}
  ): Promise<QueryResult<T>[]> {
    const opts = { ...this.defaultOptions, ...options };
    const results: QueryResult<T>[] = [];

    // Process in batches to avoid overwhelming the database
    for (let i = 0; i < operations.length; i += opts.batchSize!) {
      const batch = operations.slice(i, i + opts.batchSize!);
      const batchPromises = batch.map((op) => this.execute<T>(op.query, op.params, options));

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Execute a query with automatic retry logic
   */
  private async executeWithRetry<T>(
    query: string,
    params: any[],
    options: QueryOptions
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= (options.retries || 0); attempt++) {
      try {
        // Add timeout if specified
        if (options.timeout) {
          return await Promise.race([
            this.db.execute(query, ...params),
            this.createTimeoutPromise(options.timeout),
          ]);
        } else {
          return await this.db.execute(query, ...params);
        }
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain types of errors
        if (!this.isRetryableError(error)) {
          throw error;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < (options.retries || 0)) {
          const delay = 2 ** attempt * 1000; // 1s, 2s, 4s, etc.
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Create a timeout promise
   */
  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Query timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (!error || typeof error.message !== "string") {
      return false;
    }

    const retryableErrors = [
      "connection timeout",
      "connection refused",
      "network error",
      "temporary failure",
      "deadlock",
      "lock timeout",
    ];

    const message = error.message.toLowerCase();
    return retryableErrors.some((retryableError) => message.includes(retryableError));
  }

  /**
   * Generate a hash for query + parameters
   */
  private generateQueryHash(query: string, params: any[]): string {
    const combined = query + JSON.stringify(params);
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * Common optimized query patterns
 */
export class OptimizedQueries {
  private optimizer: QueryOptimizer;

  constructor(db: Database) {
    this.optimizer = new QueryOptimizer(db);
  }

  /**
   * Get user with caching
   */
  @cached((userId: string) => CacheKeys.user(userId), 3600)
  async getUser(userId: string) {
    return this.optimizer.execute("SELECT * FROM users WHERE id = $1", [userId], {
      cache: true,
      cacheTtl: 3600,
    });
  }

  /**
   * Get user credits with short-term caching
   */
  @cached((userId: string) => CacheKeys.userCredits(userId), 300)
  async getUserCredits(userId: string) {
    return this.optimizer.execute("SELECT credits FROM users WHERE id = $1", [userId], {
      cache: true,
      cacheTtl: 300,
    });
  }

  /**
   * Get user orders with pagination
   */
  async getUserOrders(userId: string, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    const cacheKey = CacheKeys.userOrders(userId, page, limit);

    return this.optimizer.execute(
      `SELECT * FROM orders 
       WHERE user_id = $1 
       ORDER BY created_at DESC 
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset],
      { cache: true, cacheTtl: 600 }
    );
  }

  /**
   * Get user subscription with caching
   */
  @cached((userId: string) => CacheKeys.subscription(userId), 1800)
  async getUserSubscription(userId: string) {
    return this.optimizer.execute(
      `SELECT s.*, p.name as plan_name, p.price 
       FROM subscriptions s 
       JOIN plans p ON s.plan_id = p.id 
       WHERE s.user_id = $1 AND s.status = 'active'`,
      [userId],
      { cache: true, cacheTtl: 1800 }
    );
  }

  /**
   * Bulk insert with batching
   */
  async bulkInsertCreditTransactions(transactions: any[]) {
    const operations: BatchOperation[] = transactions.map((tx) => ({
      query: `INSERT INTO credit_transactions 
              (user_id, amount, type, description, trans_no) 
              VALUES ($1, $2, $3, $4, $5)`,
      params: [tx.userId, tx.amount, tx.type, tx.description, tx.transNo],
    }));

    return this.optimizer.executeBatch(operations, {
      cache: false,
      batchSize: 50,
    });
  }

  /**
   * Complex analytics query with caching
   */
  async getDashboardStats(userId: string) {
    return this.optimizer.execute(
      `SELECT 
         COUNT(DISTINCT o.id) as total_orders,
         SUM(o.total) as total_spent,
         COUNT(DISTINCT ct.id) as total_transactions,
         SUM(CASE WHEN ct.type = 'credit' THEN ct.amount ELSE 0 END) as total_credits_earned,
         SUM(CASE WHEN ct.type = 'debit' THEN ct.amount ELSE 0 END) as total_credits_spent
       FROM users u
       LEFT JOIN orders o ON u.id = o.user_id
       LEFT JOIN credit_transactions ct ON u.id = ct.user_id
       WHERE u.id = $1`,
      [userId],
      { cache: true, cacheTtl: 1800 }
    );
  }
}

/**
 * Query performance analyzer
 */
export class QueryAnalyzer {
  private queryStats = new Map<
    string,
    {
      count: number;
      totalTime: number;
      avgTime: number;
      maxTime: number;
      minTime: number;
      errors: number;
    }
  >();

  /**
   * Record query execution stats
   */
  recordQuery(queryHash: string, executionTime: number, error?: Error) {
    const stats = this.queryStats.get(queryHash) || {
      count: 0,
      totalTime: 0,
      avgTime: 0,
      maxTime: 0,
      minTime: Infinity,
      errors: 0,
    };

    stats.count++;
    stats.totalTime += executionTime;
    stats.avgTime = stats.totalTime / stats.count;
    stats.maxTime = Math.max(stats.maxTime, executionTime);
    stats.minTime = Math.min(stats.minTime, executionTime);

    if (error) {
      stats.errors++;
    }

    this.queryStats.set(queryHash, stats);
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return Array.from(this.queryStats.entries()).map(([hash, stats]) => ({
      queryHash: hash,
      ...stats,
      errorRate: stats.errors / stats.count,
    }));
  }

  /**
   * Get slow queries
   */
  getSlowQueries(threshold = 1000) {
    return this.getStats().filter((stat) => stat.avgTime > threshold);
  }

  /**
   * Get queries with high error rates
   */
  getErrorProneQueries(threshold = 0.1) {
    return this.getStats().filter((stat) => stat.errorRate > threshold);
  }

  /**
   * Clear statistics
   */
  clearStats() {
    this.queryStats.clear();
  }
}
