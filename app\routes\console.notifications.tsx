/**
 * Notifications Management Page
 * Displays and manages user notifications
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  AlertTriangle,
  Bell,
  BellOff,
  Check,
  CheckCheck,
  CheckCircle,
  CreditCard,
  ExternalLink,
  Filter,
  Info,
  Mail,
  Settings,
  Shield,
  X,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { requireUser } from "~/core/auth/middleware.server";
import { createDbFromEnv } from "~/core/db";
import {
  dismissNotification,
  getUnreadNotificationCount,
  getUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  type NotificationType,
} from "~/core/services/notification.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Check if user is authenticated using Remix Auth
    const authUser = await requireUser(request);

    const db = context.db || createDbFromEnv();

    // Use the authenticated user's UUID
    const userUuid = authUser.uuid || authUser.id;

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);
    const unreadOnly = url.searchParams.get("unread") === "true";
    const type = url.searchParams.get("type") as NotificationType | undefined;

    const [notificationsData, unreadCount] = await Promise.all([
      getUserNotifications(userUuid, { page, limit, unreadOnly, type }, db),
      getUnreadNotificationCount(userUuid, db),
    ]);

    return json({
      success: true,
      data: {
        notifications: notificationsData.notifications,
        pagination: notificationsData.pagination,
        unreadCount,
        filters: { page, limit, unreadOnly, type },
      },
    });
  } catch (error) {
    console.error("Error loading notifications:", error);
    return json({ success: false, error: "Failed to load notifications" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    // Check if user is authenticated using Remix Auth
    const authUser = await requireUser(request);

    const db = context.db || createDbFromEnv();

    // Use the authenticated user's UUID
    const userUuid = authUser.uuid || authUser.id;

    const formData = await request.formData();
    const action = formData.get("action") as string;

    switch (action) {
      case "mark-read": {
        const notificationId = parseInt(formData.get("notificationId") as string, 10);
        const result = await markNotificationAsRead(notificationId, userUuid, db);

        return json({
          success: result.success,
          message: result.success ? "Notification marked as read" : result.error,
        });
      }

      case "dismiss": {
        const notificationId = parseInt(formData.get("notificationId") as string, 10);
        const result = await dismissNotification(notificationId, userUuid, db);

        return json({
          success: result.success,
          message: result.success ? "Notification dismissed" : result.error,
        });
      }

      case "mark-all-read": {
        const result = await markAllNotificationsAsRead(userUuid, db);

        return json({
          success: result.success,
          message: result.success ? "All notifications marked as read" : result.error,
        });
      }

      default:
        return json({ success: false, error: "Invalid action" });
    }
  } catch (error) {
    console.error("Error processing notification action:", error);
    return json({ success: false, error: "Failed to process request" }, { status: 500 });
  }
}

export default function NotificationsPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [selectedType, setSelectedType] = useState<string>("all");

  const isSubmitting = navigation.state === "submitting";

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case "payment":
        return <CreditCard className="w-5 h-5 text-blue-500" />;
      case "credit":
        return <Zap className="w-5 h-5 text-yellow-500" />;
      case "security":
        return <Shield className="w-5 h-5 text-red-500" />;
      case "system":
        return <Settings className="w-5 h-5 text-gray-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "payment":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "credit":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "security":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "system":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
    }
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  const notificationTypes = [
    { value: "all", label: "All", icon: Bell },
    { value: "info", label: "Info", icon: Info },
    { value: "success", label: "Success", icon: CheckCircle },
    { value: "warning", label: "Warning", icon: AlertTriangle },
    { value: "error", label: "Error", icon: XCircle },
    { value: "payment", label: "Payment", icon: CreditCard },
    { value: "credit", label: "Credits", icon: Zap },
    { value: "security", label: "Security", icon: Shield },
    { value: "system", label: "System", icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Notifications</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Stay updated with your account activity and system updates
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {data.unreadCount > 0 && (
                <Form method="post" className="inline">
                  <input type="hidden" name="action" value="mark-all-read" />
                  <Button type="submit" variant="outline" size="sm" disabled={isSubmitting}>
                    <CheckCheck className="w-4 h-4 mr-2" />
                    Mark All Read
                  </Button>
                </Form>
              )}
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Bell className="w-4 h-4" />
                <span>{data.unreadCount} unread</span>
              </Badge>
            </div>
          </div>
        </div>

        {/* Action Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span>{actionData.message}</span>
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>Filter Notifications</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={selectedType} onValueChange={setSelectedType}>
              <TabsList className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-1">
                {notificationTypes.map((type) => (
                  <TabsTrigger
                    key={type.value}
                    value={type.value}
                    className="flex items-center space-x-1"
                  >
                    <type.icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{type.label}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Notifications</CardTitle>
            <CardDescription>
              {data.notifications.length === 0
                ? "No notifications found"
                : `Showing ${data.notifications.length} of ${data.pagination.totalCount} notifications`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.notifications.length === 0 ? (
              <div className="text-center py-12">
                <BellOff className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No notifications</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You're all caught up! New notifications will appear here.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {data.notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 border rounded-lg transition-colors ${
                      notification.readAt
                        ? "bg-gray-50 dark:bg-gray-800/50"
                        : "bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-800"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-semibold text-gray-900 dark:text-white">
                              {notification.title}
                            </h4>
                            <Badge className={getNotificationColor(notification.type)}>
                              {notification.type}
                            </Badge>
                            {!notification.readAt && (
                              <Badge variant="secondary" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-2">
                            {notification.body}
                          </p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>{formatRelativeTime(notification.createdAt)}</span>
                            {notification.link && (
                              <a
                                href={notification.link}
                                className="flex items-center space-x-1 text-blue-600 hover:text-blue-500"
                              >
                                <span>View details</span>
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        {!notification.readAt && (
                          <Form method="post" className="inline">
                            <input type="hidden" name="action" value="mark-read" />
                            <input type="hidden" name="notificationId" value={notification.id} />
                            <Button type="submit" variant="ghost" size="sm" disabled={isSubmitting}>
                              <Check className="w-4 h-4" />
                            </Button>
                          </Form>
                        )}

                        {!notification.dismissed && (
                          <Form method="post" className="inline">
                            <input type="hidden" name="action" value="dismiss" />
                            <input type="hidden" name="notificationId" value={notification.id} />
                            <Button type="submit" variant="ghost" size="sm" disabled={isSubmitting}>
                              <X className="w-4 h-4" />
                            </Button>
                          </Form>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasPrev}>
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Page {data.pagination.currentPage} of {data.pagination.totalPages}
                  </span>
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasNext}>
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
