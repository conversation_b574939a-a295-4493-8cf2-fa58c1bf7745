// Database operations and query helpers for common use cases
import { and, asc, count, desc, eq, sql } from "drizzle-orm";
import { getUuid } from "~/core/auth/hash";
import type { Database } from "./db";
import * as schema from "./schema";

// Generic pagination interface
export interface PaginationOptions {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Generic filter interface
export interface FilterOptions {
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters?: Record<string, any>;
}

// Database transaction wrapper
export async function withTransaction<T>(
  db: Database,
  callback: (tx: Parameters<Parameters<Database["transaction"]>[0]>[0]) => Promise<T>
): Promise<T> {
  try {
    return await db.transaction(callback);
  } catch (error) {
    console.error("Transaction failed:", error);
    throw error;
  }
}

// Pagination utility
export function calculatePagination(page, limit, total: number) {
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;

  return {
    page,
    limit,
    offset,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

// Generic paginated query function
export async function paginatedQuery<T>(
  db: Database,
  query: any,
  options: PaginationOptions = {}
): Promise<PaginationResult<T>> {
  const { page = 1, limit = 10 } = options;
  const offset = (page - 1) * limit;

  // Get total count
  const [totalResult] = await db.select({ count: count() }).from(query);
  const total = totalResult?.count || 0;

  // Get paginated data
  const data = await query.limit(limit).offset(offset);

  return {
    data,
    pagination: calculatePagination(page, limit, total),
  };
}

// User operations
export const userOperations = {
  // Find user by email
  async findByEmail(db: Database, email: string) {
    const [user] = await db
      .select()
      .from(schema.users)
      .where(eq(schema.users.email, email))
      .limit(1);
    return user || null;
  },

  // Find user by UUID
  async findByUuid(db: Database, uuid: string) {
    const [user] = await db.select().from(schema.users).where(eq(schema.users.uuid, uuid)).limit(1);
    return user || null;
  },

  // Create user with account
  async createWithAccount(
    db: Database,
    userData: schema.NewUser,
    accountData: Omit<schema.NewAccount, "primaryOwnerUserId">
  ) {
    return await withTransaction(db, async (tx) => {
      // Create user first
      const [user] = await tx
        .insert(schema.users)
        .values({
          id: getUuid(),
          uuid: getUuid(),
          ...userData,
        })
        .returning();

      // Create personal account
      const [account] = await tx
        .insert(schema.accounts)
        .values({
          id: getUuid(),
          ...accountData,
          primaryOwnerUserId: user.id,
          isPersonalAccount: true,
        })
        .returning();

      // Create account membership
      await tx.insert(schema.accountsMemberships).values({
        accountId: account.id,
        userId: user.id,
        accountRole: "owner", // Assuming 'owner' role exists
      });

      return { user, account };
    });
  },

  // Update user credits
  async updateCredits(
    db: Database,
    userUuid: string,
    creditChange: number,
    transactionType: string,
    description?: string
  ) {
    return await withTransaction(db, async (tx) => {
      // Update user credits
      const [user] = await tx
        .update(schema.users)
        .set({
          credits: sql`${schema.users.credits} + ${creditChange}`,
          updatedAt: new Date(),
        })
        .where(eq(schema.users.uuid, userUuid))
        .returning();

      if (!user) {
        throw new Error("User not found");
      }

      // Create credit transaction record
      await tx.insert(schema.creditTransactions).values({
        userUuid,
        transType: transactionType,
        credits: creditChange,
        description,
        transNo: `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      });

      return user;
    });
  },
};

// Account operations
export const accountOperations = {
  // Get account with members
  async getWithMembers(db: Database, accountId: string) {
    return await db
      .select({
        account: schema.accounts,
        user: schema.users,
        membership: schema.accountsMemberships,
        role: schema.roles,
      })
      .from(schema.accounts)
      .leftJoin(
        schema.accountsMemberships,
        eq(schema.accounts.id, schema.accountsMemberships.accountId)
      )
      .leftJoin(schema.users, eq(schema.accountsMemberships.userId, schema.users.id))
      .leftJoin(schema.roles, eq(schema.accountsMemberships.accountRole, schema.roles.name))
      .where(eq(schema.accounts.id, accountId));
  },

  // Check if user has access to account
  async hasAccess(db: Database, userId: string, accountId: string) {
    const [membership] = await db
      .select()
      .from(schema.accountsMemberships)
      .where(
        and(
          eq(schema.accountsMemberships.userId, userId),
          eq(schema.accountsMemberships.accountId, accountId)
        )
      )
      .limit(1);
    return !!membership;
  },
};

// Order operations
export const orderOperations = {
  // Get order with items
  async getWithItems(db: Database, orderId: string) {
    return await db
      .select({
        order: schema.orders,
        item: schema.orderItems,
      })
      .from(schema.orders)
      .leftJoin(schema.orderItems, eq(schema.orders.id, schema.orderItems.orderId))
      .where(eq(schema.orders.id, orderId));
  },

  // Get user orders with pagination
  async getUserOrders(
    db: Database,
    userUuid: string,
    options: PaginationOptions & FilterOptions = {}
  ) {
    const { page = 1, limit = 10, sortOrder = "desc" } = options;
    const offset = (page - 1) * limit;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(schema.orders)
      .where(eq(schema.orders.userUuid, userUuid));
    const total = totalResult?.count || 0;

    // Get paginated data with sorting
    const data = await db
      .select()
      .from(schema.orders)
      .where(eq(schema.orders.userUuid, userUuid))
      .orderBy(sortOrder === "desc" ? desc(schema.orders.createdAt) : asc(schema.orders.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      data,
      pagination: calculatePagination(page, limit, total),
    };
  },
};

// Credit operations
export const creditOperations = {
  // Get user credit history
  async getUserCreditHistory(db: Database, userUuid: string, options: PaginationOptions = {}) {
    const query = db
      .select()
      .from(schema.creditTransactions)
      .where(eq(schema.creditTransactions.userUuid, userUuid))
      .orderBy(desc(schema.creditTransactions.createdAt));

    return await paginatedQuery(db, query, options);
  },

  // Get user credit balance
  async getUserCreditBalance(db: Database, userUuid: string) {
    const [user] = await db
      .select({ credits: schema.users.credits })
      .from(schema.users)
      .where(eq(schema.users.uuid, userUuid))
      .limit(1);

    return user?.credits || 0;
  },
};

// Health check operations
export const healthOperations = {
  // Check database connectivity
  async checkConnection(db: Database): Promise<boolean> {
    try {
      await db.execute(sql`SELECT 1`);
      return true;
    } catch (error) {
      console.error("Database connection check failed:", error);
      return false;
    }
  },

  // Get database statistics
  async getStats(db: Database) {
    try {
      const [userCount] = await db.select({ count: count() }).from(schema.users);
      const [accountCount] = await db.select({ count: count() }).from(schema.accounts);
      const [orderCount] = await db.select({ count: count() }).from(schema.orders);

      return {
        users: userCount.count,
        accounts: accountCount.count,
        orders: orderCount.count,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error("Failed to get database stats:", error);
      throw error;
    }
  },
};

// Session operations
export const sessionOperations = {
  // Create new session
  async create(db: Database, sessionData: schema.NewSession) {
    const [session] = await db
      .insert(schema.sessions)
      .values({
        id: getUuid(),
        ...sessionData,
      })
      .returning();
    return session;
  },

  // Find active session by token
  async findByToken(db: Database, sessionToken: string) {
    const [session] = await db
      .select()
      .from(schema.sessions)
      .where(
        and(eq(schema.sessions.sessionToken, sessionToken), eq(schema.sessions.isActive, true))
      )
      .limit(1);
    return session || null;
  },

  // Find session by ID
  async findById(db: Database, sessionId: string) {
    const [session] = await db
      .select()
      .from(schema.sessions)
      .where(eq(schema.sessions.id, sessionId))
      .limit(1);
    return session || null;
  },

  // Deactivate session
  async deactivate(db: Database, sessionId: string) {
    await db
      .update(schema.sessions)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(schema.sessions.id, sessionId));
  },

  // Deactivate all user sessions
  async deactivateAllUserSessions(db: Database, userId: string) {
    await db
      .update(schema.sessions)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(schema.sessions.userId, userId));
  },

  // Clean up expired sessions
  async cleanupExpired(db: Database) {
    const now = new Date();
    await db
      .update(schema.sessions)
      .set({ isActive: false })
      .where(and(eq(schema.sessions.isActive, true), sql`${schema.sessions.expiresAt} < ${now}`));
  },

  // Get user sessions
  async getUserSessions(db: Database, userId: string, activeOnly = true) {
    const conditions = [eq(schema.sessions.userId, userId)];
    if (activeOnly) {
      conditions.push(eq(schema.sessions.isActive, true));
    }

    return await db
      .select()
      .from(schema.sessions)
      .where(and(...conditions))
      .orderBy(desc(schema.sessions.createdAt));
  },
};

// Auth operations
export const authOperations = {
  // Create user with Google OAuth
  async createGoogleUser(
    db: Database,
    userData: {
      email: string;
      name: string;
      avatar?: string;
      googleId: string;
      ipAddress?: string;
    }
  ) {
    return await withTransaction(db, async (tx) => {
      // Create user
      const [user] = await tx
        .insert(schema.users)
        .values({
          id: getUuid(),
          uuid: getUuid(),
          email: userData.email,
          name: userData.name,
          avatar: userData.avatar,
          credits: 100, // Initial credits
          signinType: "oauth",
          signinProvider: "google",
          signinOpenid: userData.googleId,
          signinIp: userData.ipAddress,
          isAffiliate: false,
        })
        .returning();

      // Create personal account
      const [account] = await tx
        .insert(schema.accounts)
        .values({
          id: getUuid(),
          name: `${userData.name}'s Account`,
          primaryOwnerUserId: user.id,
          isPersonalAccount: true,
        })
        .returning();

      // Create account membership
      await tx.insert(schema.accountsMemberships).values({
        accountId: account.id,
        userId: user.id,
        accountRole: "owner",
      });

      return { user, account };
    });
  },

  // Update user from Google OAuth
  async updateGoogleUser(
    db: Database,
    userId: string,
    userData: {
      name: string;
      avatar?: string;
      googleId: string;
      ipAddress?: string;
    }
  ) {
    const [user] = await db
      .update(schema.users)
      .set({
        name: userData.name,
        avatar: userData.avatar,
        signinOpenid: userData.googleId,
        signinIp: userData.ipAddress,
        updatedAt: new Date(),
      })
      .where(eq(schema.users.id, userId))
      .returning();

    return user;
  },

  // Get user with session
  async getUserWithSession(db: Database, sessionToken: string) {
    const result = await db
      .select({
        user: schema.users,
        session: schema.sessions,
      })
      .from(schema.sessions)
      .innerJoin(schema.users, eq(schema.sessions.userId, schema.users.id))
      .where(
        and(eq(schema.sessions.sessionToken, sessionToken), eq(schema.sessions.isActive, true))
      )
      .limit(1);

    return result[0] || null;
  },
};

// Export all operations as a unified interface
export const dbOperations = {
  user: userOperations,
  account: accountOperations,
  order: orderOperations,
  credit: creditOperations,
  health: healthOperations,
  session: sessionOperations,
  auth: authOperations,
  withTransaction,
  paginatedQuery,
  calculatePagination,
};
