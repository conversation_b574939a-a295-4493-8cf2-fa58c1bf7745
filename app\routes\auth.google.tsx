/**
 * Google OAuth Authentication Route
 * Handles Google OAuth login flow using Lucia 3 + Arctic
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { generateState, generateCodeVerifier } from "arctic";
import { google } from "~/core/auth/lucia.server";

// Handle Google OAuth login initiation
export async function loader({ request }: LoaderFunctionArgs) {
  const state = generateState();
  const codeVerifier = generateCodeVerifier();
  const url = google.createAuthorizationURL(state, codeVerifier, {
    scopes: ["profile", "email"],
  });

  // Store state and code verifier in session cookies
  const headers = new Headers();
  headers.append(
    "Set-Cookie",
    `google_oauth_state=${state}; Path=/; HttpOnly; SameSite=Lax; Max-Age=600; ${
      process.env.NODE_ENV === "production" ? "Secure;" : ""
    }`
  );
  headers.append(
    "Set-Cookie", 
    `google_code_verifier=${codeVerifier}; Path=/; HttpOnly; SameSite=Lax; Max-Age=600; ${
      process.env.NODE_ENV === "production" ? "Secure;" : ""
    }`
  );

  return redirect(url.toString(), { headers });
}

// Handle form submission (if any)
export async function action({ request }: ActionFunctionArgs) {
  return await loader({ request });
}

// This component should never render as the loader redirects
export default function GoogleAuth() {
  return <div>Redirecting to Google...</div>;
}
