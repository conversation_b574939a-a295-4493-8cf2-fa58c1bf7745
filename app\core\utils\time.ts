/**
 * Time utility functions
 */

/**
 * Get current ISO timestamp string
 */
export function getIsoTimestr(): string {
  return new Date().toISOString();
}

/**
 * Add months to a date
 */
export function addMonths(date: Date, months: number): Date {
  const result = new Date(date);
  result.setMonth(result.getMonth() + months);
  return result;
}

/**
 * Add days to a date
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Format date to ISO string
 */
export function formatToIso(date: Date): string {
  return date.toISOString();
}
