/**
 * API Authentication Middleware
 * Handles API key validation and user authentication for API endpoints
 */

import type { Database } from "~/core/db/db";
import type { Api<PERSON><PERSON> } from "~/core/db/schema";
import { updateApiKeyLastUsed, validateApi<PERSON>ey } from "~/core/services/api-key.server";

export interface ApiAuthResult {
  isAuthenticated: boolean;
  apiKey?: ApiKey;
  userUuid?: string;
  error?: string;
}

export interface ApiAuthContext {
  apiKey: ApiKey;
  userUuid: string;
}

/**
 * Extract API key from request headers
 */
export function extractApiKey(request: Request): string | null {
  // Check Authorization header (Bearer token)
  const authHeader = request.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // Check X-API-Key header
  const apiKeyHeader = request.headers.get("X-API-Key");
  if (apiKeyHeader) {
    return apiKeyHeader;
  }

  // Check query parameter (less secure, but sometimes needed)
  const url = new URL(request.url);
  const apiKeyParam = url.searchParams.get("api_key");
  if (apiKeyParam) {
    return apiKeyParam;
  }

  return null;
}

/**
 * Authenticate API request using API key
 */
export async function authenticateApiRequest(
  request: Request,
  db: Database
): Promise<ApiAuthResult> {
  try {
    const apiKeyString = extractApiKey(request);

    if (!apiKeyString) {
      return {
        isAuthenticated: false,
        error:
          "API key is required. Provide it via Authorization header (Bearer token), X-API-Key header, or api_key query parameter.",
      };
    }

    const validation = await validateApiKey(apiKeyString, db);

    if (!validation.isValid || !validation.apiKey) {
      return {
        isAuthenticated: false,
        error: validation.error || "Invalid API key",
      };
    }

    // Update last used timestamp (async, don't wait)
    updateApiKeyLastUsed(apiKeyString, db).catch((error) => {
      console.error("Failed to update API key last used:", error);
    });

    return {
      isAuthenticated: true,
      apiKey: validation.apiKey,
      userUuid: validation.apiKey.userUuid,
    };
  } catch (error) {
    console.error("Error authenticating API request:", error);
    return {
      isAuthenticated: false,
      error: "Authentication failed",
    };
  }
}

/**
 * Middleware function to require API authentication
 */
export async function requireApiAuth(request: Request, db: Database): Promise<ApiAuthContext> {
  const authResult = await authenticateApiRequest(request, db);

  if (!authResult.isAuthenticated || !authResult.apiKey || !authResult.userUuid) {
    throw new Response(
      JSON.stringify({
        error: "Authentication required",
        message: authResult.error || "Invalid or missing API key",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  return {
    apiKey: authResult.apiKey,
    userUuid: authResult.userUuid,
  };
}

/**
 * Check if API key has specific permissions (placeholder for future implementation)
 */
export function hasApiPermission(apiKey: ApiKey, permission: string): boolean {
  // TODO: Implement permission system
  // For now, all active API keys have full permissions
  return apiKey.status === "active";
}

/**
 * Rate limiting for API keys (placeholder for future implementation)
 */
export async function checkApiRateLimit(
  apiKey: ApiKey,
  endpoint: string
): Promise<{ allowed: boolean; remaining?: number; resetTime?: Date }> {
  // TODO: Implement rate limiting based on API key
  // For now, allow all requests
  return { allowed: true };
}

/**
 * Log API usage for analytics
 */
export async function logApiUsage(
  apiKey: ApiKey,
  request: Request,
  response: Response,
  startTime: number
): Promise<void> {
  try {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const url = new URL(request.url);

    // TODO: Store API usage data for analytics
    console.log("API Usage:", {
      apiKeyId: apiKey.id,
      userUuid: apiKey.userUuid,
      method: request.method,
      endpoint: url.pathname,
      statusCode: response.status,
      duration,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Failed to log API usage:", error);
  }
}

/**
 * Create API error response
 */
export function createApiErrorResponse(message: string, statusCode = 400, details?: any): Response {
  return new Response(
    JSON.stringify({
      error: true,
      message,
      code: statusCode,
      details,
      timestamp: new Date().toISOString(),
    }),
    {
      status: statusCode,
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

/**
 * Create API success response
 */
export function createApiSuccessResponse(data: any, message?: string): Response {
  return new Response(
    JSON.stringify({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    }),
    {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

/**
 * Wrapper function for API endpoints with authentication
 */
export function withApiAuth<T extends any[]>(
  handler: (authContext: ApiAuthContext, ...args: T) => Promise<Response>
) {
  return async (request: Request, db: Database, ...args: T): Promise<Response> => {
    const startTime = Date.now();

    try {
      const authContext = await requireApiAuth(request, db);
      const response = await handler(authContext, ...args);

      // Log API usage
      logApiUsage(authContext.apiKey, request, response, startTime).catch((error) => {
        console.error("Failed to log API usage:", error);
      });

      return response;
    } catch (error) {
      if (error instanceof Response) {
        return error;
      }

      console.error("API endpoint error:", error);
      return createApiErrorResponse(
        "Internal server error",
        500,
        process.env.NODE_ENV === "development" ? error : undefined
      );
    }
  };
}
