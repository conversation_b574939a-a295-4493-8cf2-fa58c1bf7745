// Common formatting utilities

/**
 * Number formatting utilities
 */
export class NumberFormatter {
  /**
   * Format number as currency
   */
  static currency(amount: number, currency = "USD", locale = "en-US"): string {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency,
    }).format(amount);
  }

  /**
   * Format number with thousands separators
   */
  static number(value: number, locale = "en-US", options?: Intl.NumberFormatOptions): string {
    return new Intl.NumberFormat(locale, options).format(value);
  }

  /**
   * Format as percentage
   */
  static percentage(value: number, decimals = 2, locale = "en-US"): string {
    return new Intl.NumberFormat(locale, {
      style: "percent",
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  }

  /**
   * Format file size in bytes to human readable format
   */
  static fileSize(bytes: number, decimals = 2): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB"];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  /**
   * Format duration in milliseconds to human readable format
   */
  static duration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    if (seconds > 0) {
      return `${seconds}s`;
    }
    return `${ms}ms`;
  }

  /**
   * Abbreviate large numbers (1K, 1M, 1B)
   */
  static abbreviate(value: number, decimals = 1): string {
    if (value < 1000) {
      return value.toString();
    }

    const suffixes = ["", "K", "M", "B", "T"];
    const suffixNum = Math.floor(Math.log10(value) / 3);
    const shortValue = parseFloat((value / 1000 ** suffixNum).toFixed(decimals));

    return `${shortValue}${suffixes[suffixNum]}`;
  }
}

/**
 * String formatting utilities
 */
export class StringFormatter {
  /**
   * Capitalize first letter
   */
  static capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  /**
   * Convert to title case
   */
  static titleCase(str: string): string {
    return str.replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  /**
   * Convert to kebab case
   */
  static kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1-$2")
      .replace(/[\s_]+/g, "-")
      .toLowerCase();
  }

  /**
   * Convert to camel case
   */
  static camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }

  /**
   * Convert to snake case
   */
  static snakeCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1_$2")
      .replace(/[\s-]+/g, "_")
      .toLowerCase();
  }

  /**
   * Truncate string with ellipsis
   */
  static truncate(str: string, length: number, suffix = "..."): string {
    if (str.length <= length) return str;
    return str.slice(0, length - suffix.length) + suffix;
  }

  /**
   * Remove HTML tags
   */
  static stripHtml(html: string): string {
    return html.replace(/<[^>]*>?/gm, "");
  }

  /**
   * Generate slug from string
   */
  static slug(str: string): string {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
  }

  /**
   * Extract initials from name
   */
  static initials(name: string, maxLength = 2): string {
    return name
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase())
      .slice(0, maxLength)
      .join("");
  }

  /**
   * Mask sensitive information
   */
  static mask(str: string, visibleStart = 4, visibleEnd = 4, maskChar = "*"): string {
    if (str.length <= visibleStart + visibleEnd) {
      return str;
    }

    const start = str.slice(0, visibleStart);
    const end = str.slice(-visibleEnd);
    const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);

    return start + middle + end;
  }

  /**
   * Format phone number
   */
  static phone(phone: string, format = "(XXX) XXX-XXXX"): string {
    const cleaned = phone.replace(/\D/g, "");

    if (cleaned.length === 10) {
      return format
        .replace(/X/g, () => cleaned.charAt(format.indexOf("X")))
        .replace(/XXX/, cleaned.slice(0, 3))
        .replace(/XXX/, cleaned.slice(3, 6))
        .replace(/XXXX/, cleaned.slice(6, 10));
    }

    return phone; // Return original if not 10 digits
  }

  /**
   * Format credit card number
   */
  static creditCard(cardNumber: string): string {
    const cleaned = cardNumber.replace(/\D/g, "");
    return cleaned.replace(/(.{4})/g, "$1 ").trim();
  }
}

/**
 * Date formatting utilities
 */
export class DateFormatter {
  /**
   * Format date to relative time (e.g., "2 hours ago")
   */
  static relative(date: Date | string, locale = "en-US"): string {
    const now = new Date();
    const target = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);

    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: "auto" });

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, "second");
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return rtf.format(-diffInMinutes, "minute");
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return rtf.format(-diffInHours, "hour");
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return rtf.format(-diffInDays, "day");
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return rtf.format(-diffInMonths, "month");
    }

    const diffInYears = Math.floor(diffInMonths / 12);
    return rtf.format(-diffInYears, "year");
  }

  /**
   * Format date to local string
   */
  static local(
    date: Date | string,
    locale = "en-US",
    options?: Intl.DateTimeFormatOptions
  ): string {
    return new Date(date).toLocaleDateString(locale, options);
  }

  /**
   * Format time to local string
   */
  static time(date: Date | string, locale = "en-US", options?: Intl.DateTimeFormatOptions): string {
    return new Date(date).toLocaleTimeString(locale, options);
  }

  /**
   * Format date and time to local string
   */
  static dateTime(
    date: Date | string,
    locale = "en-US",
    options?: Intl.DateTimeFormatOptions
  ): string {
    return new Date(date).toLocaleString(locale, options);
  }

  /**
   * Format date to ISO string
   */
  static iso(date: Date | string): string {
    return new Date(date).toISOString();
  }

  /**
   * Format date for input[type="date"]
   */
  static inputDate(date: Date | string): string {
    return new Date(date).toISOString().split("T")[0];
  }

  /**
   * Format time for input[type="time"]
   */
  static inputTime(date: Date | string): string {
    return new Date(date).toISOString().split("T")[1].slice(0, 5);
  }
}

// Export convenience functions
export const format = {
  currency: NumberFormatter.currency,
  number: NumberFormatter.number,
  percentage: NumberFormatter.percentage,
  fileSize: NumberFormatter.fileSize,
  duration: NumberFormatter.duration,
  abbreviate: NumberFormatter.abbreviate,

  capitalize: StringFormatter.capitalize,
  titleCase: StringFormatter.titleCase,
  kebabCase: StringFormatter.kebabCase,
  camelCase: StringFormatter.camelCase,
  snakeCase: StringFormatter.snakeCase,
  truncate: StringFormatter.truncate,
  stripHtml: StringFormatter.stripHtml,
  slug: StringFormatter.slug,
  initials: StringFormatter.initials,
  mask: StringFormatter.mask,
  phone: StringFormatter.phone,
  creditCard: StringFormatter.creditCard,

  relative: DateFormatter.relative,
  local: DateFormatter.local,
  time: DateFormatter.time,
  dateTime: DateFormatter.dateTime,
  iso: DateFormatter.iso,
  inputDate: DateFormatter.inputDate,
  inputTime: DateFormatter.inputTime,
};
