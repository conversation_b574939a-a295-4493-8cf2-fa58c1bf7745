import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import AIChatInterface from "~/components/ai-chat-interface/index";
import Showcase from "~/components/blocks/showcase";
import Sidebar, { type ChatConversation } from "~/components/layout/sidebar";
import UnifiedLayout from "~/components/layout/unified-layout";
import WelcomeFlow from "~/components/onboarding/welcome-flow";
import { getLandingPageConfig } from "~/config/landing";
import { siteConfig } from "~/config/seo";
import { getUser } from "~/core/auth/middleware.server";
import { generateStructuredData } from "~/core/seo/seo";
import { shouldShowOnboarding } from "~/core/services/onboarding.server";

export const meta: MetaFunction = () => {
  const title = "Remix Starter - AI-Powered SaaS Platform";
  const description =
    "Build amazing AI applications with Remix, Vercel, and Neon Database. Get started in minutes, not months.";

  return [
    { title },
    { name: "description", content: description },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    {
      "script:ld+json": generateStructuredData({
        type: "WebSite",
        name: title,
        description,
        url: siteConfig.url,
      }),
    },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const config = getLandingPageConfig();

    // Check if user is authenticated
    const userResult = await getUser(request);

    let user = null;
    let showOnboarding = false;

    if (userResult.success && userResult.user) {
      user = userResult.user;
      showOnboarding = shouldShowOnboarding(user, request);
    }

    return json({
      config,
      user,
      showOnboarding,
      isNewUser: new URL(request.url).searchParams.get("new_user") === "true",
    });
  } catch (error) {
    console.error("Error loading index page:", error);
    const config = getLandingPageConfig();
    return json({
      config,
      user: null,
      showOnboarding: false,
      isNewUser: false,
    });
  }
}

function ChatSidebarWrapper() {
  const { user } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState(false);
  const currentConversationId = searchParams.get("conversation");

  const loadConversations = async () => {
    // Don't try to load conversations if user is not logged in
    if (!user) {
      setIsLoading(false);
      setAuthError(true);
      return;
    }

    setIsLoading(true);
    setAuthError(false);
    try {
      const response = await fetch("/api/chat/conversations");
      if (response.ok) {
        const data = (await response.json()) as any;
        if (data.success) {
          setConversations(data.data.conversations || []);
        } else {
          // Handle API error but don't crash the UI
          console.warn("API returned error:", data.error);
          setConversations([]);
        }
      } else if (response.status === 401) {
        // User is not authenticated
        setAuthError(true);
        setConversations([]);
      } else {
        console.warn("Failed to fetch conversations:", response.status, response.statusText);
        setConversations([]);
      }
    } catch (error) {
      console.error("Failed to load conversations:", error);
      // Set empty conversations instead of crashing
      setConversations([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load conversations
  useEffect(() => {
    loadConversations();
  }, []);

  const handleNewChat = () => {
    setSearchParams({});
  };

  const handleSelectConversation = (conversationId: string) => {
    setSearchParams({ conversation: conversationId });
  };

  const handleArchiveConversation = async (conversationId: string) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "archive",
          conversationId,
        }),
      });

      if (response.ok) {
        // Remove from local state
        setConversations((prev) => prev.filter((conv) => conv.id !== conversationId));

        // If this was the current conversation, clear it
        if (currentConversationId === conversationId) {
          setSearchParams({});
        }
      }
    } catch (error) {
      console.error("Failed to archive conversation:", error);
    }
  };

  return (
    <Sidebar
      mode="chat"
      title="AI Chat"
      showCloseButton={false}
      conversations={conversations}
      currentConversationId={currentConversationId || undefined}
      onNewChat={handleNewChat}
      onSelectConversation={handleSelectConversation}
      onArchiveConversation={handleArchiveConversation}
      isLoadingConversations={isLoading}
      authError={authError}
      user={user}
    />
  );
}

export default function Index() {
  const { config, user, showOnboarding, isNewUser } = useLoaderData<typeof loader>();
  const [showWelcomeFlow, setShowWelcomeFlow] = useState(false);

  // Use useEffect to set the welcome flow state after hydration
  useEffect(() => {
    setShowWelcomeFlow(showOnboarding && isNewUser);
  }, [showOnboarding, isNewUser]);

  const handleOnboardingComplete = async () => {
    try {
      await fetch("/api/onboarding/complete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });
      setShowWelcomeFlow(false);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      setShowWelcomeFlow(false);
    }
  };

  const handleOnboardingSkip = () => {
    setShowWelcomeFlow(false);
  };

  return (
    <>
      <UnifiedLayout
        showSidebar={true}
        showHeader={false}
        showFooter={false}
        customSidebar={<ChatSidebarWrapper />}
        className="bg-background"
      >
        <div className="relative z-10 h-full flex flex-col">
          {/* Chat Interface */}
          <div className="flex-1 max-w-4xl mx-auto w-full px-4">
            <AIChatInterface className="h-full" user={user} />
          </div>

          {/* Showcase Section */}
          <div className="mt-8">
            <Showcase title={config.showcase.title} items={config.showcase.items} />
          </div>
        </div>
      </UnifiedLayout>

      {/* Welcome Flow for New Users */}
      {showWelcomeFlow && user && (
        <WelcomeFlow
          user={{
            name: user.name,
            email: user.email,
            credits: user.credits || 0,
            inviteCode: (user as any).inviteCode,
          }}
          isNewUser={isNewUser}
          onComplete={handleOnboardingComplete}
          onSkip={handleOnboardingSkip}
        />
      )}
    </>
  );
}
