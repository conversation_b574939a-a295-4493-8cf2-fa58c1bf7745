/**
 * Environment Configuration Management System
 * Manages environment variables, secrets, and configuration across environments
 */

export interface EnvironmentVariable {
  key: string;
  value: string;
  encrypted: boolean;
  description?: string;
  required: boolean;
  environment: string;
  lastUpdated: Date;
  updatedBy: string;
}

export interface SecretConfig {
  key: string;
  description: string;
  required: boolean;
  environments: string[];
  lastRotated?: Date;
  rotationInterval?: number; // days
}

export interface ConfigTemplate {
  name: string;
  description: string;
  variables: Array<{
    key: string;
    defaultValue?: string;
    description: string;
    required: boolean;
    type: "string" | "number" | "boolean" | "json";
    validation?: string; // regex pattern
  }>;
}

export interface EnvironmentHealth {
  environment: string;
  status: "healthy" | "warning" | "critical";
  checks: Array<{
    name: string;
    status: "pass" | "fail" | "warning";
    message: string;
    lastCheck: Date;
  }>;
  missingVariables: string[];
  expiredSecrets: string[];
  lastHealthCheck: Date;
}

// Configuration templates for different environments
const configTemplates: ConfigTemplate[] = [
  {
    name: "remix-cloudflare-app",
    description: "Standard configuration for Remix + Cloudflare Workers application",
    variables: [
      {
        key: "NODE_ENV",
        defaultValue: "production",
        description: "Node.js environment mode",
        required: true,
        type: "string",
        validation: "^(development|staging|production)$",
      },
      {
        key: "DATABASE_URL",
        description: "Neon PostgreSQL database connection URL",
        required: true,
        type: "string",
        validation: "^postgresql://.+",
      },
      {
        key: "OPENAI_API_KEY",
        description: "OpenAI API key for AI services",
        required: false,
        type: "string",
        validation: "^sk-.+",
      },
      {
        key: "ANTHROPIC_API_KEY",
        description: "Anthropic API key for Claude AI",
        required: false,
        type: "string",
        validation: "^sk-.+",
      },
      {
        key: "STRIPE_PUBLISHABLE_KEY",
        description: "Stripe publishable key for payments",
        required: false,
        type: "string",
        validation: "^pk_.+",
      },
      {
        key: "STRIPE_SECRET_KEY",
        description: "Stripe secret key for payments",
        required: false,
        type: "string",
        validation: "^sk_.+",
      },
      {
        key: "JWT_SECRET",
        description: "JWT signing secret for authentication",
        required: true,
        type: "string",
      },
      {
        key: "ENCRYPTION_KEY",
        description: "Encryption key for sensitive data",
        required: true,
        type: "string",
      },
      {
        key: "LOG_LEVEL",
        defaultValue: "info",
        description: "Application logging level",
        required: false,
        type: "string",
        validation: "^(debug|info|warn|error)$",
      },
      {
        key: "RATE_LIMIT_ENABLED",
        defaultValue: "true",
        description: "Enable rate limiting",
        required: false,
        type: "boolean",
      },
      {
        key: "CACHE_TTL",
        defaultValue: "300",
        description: "Default cache TTL in seconds",
        required: false,
        type: "number",
      },
    ],
  },
];

// Secret configurations
const secretConfigs: SecretConfig[] = [
  {
    key: "DATABASE_URL",
    description: "Database connection string",
    required: true,
    environments: ["development", "staging", "production"],
    rotationInterval: 90,
  },
  {
    key: "JWT_SECRET",
    description: "JWT signing secret",
    required: true,
    environments: ["development", "staging", "production"],
    rotationInterval: 30,
  },
  {
    key: "ENCRYPTION_KEY",
    description: "Data encryption key",
    required: true,
    environments: ["development", "staging", "production"],
    rotationInterval: 60,
  },
  {
    key: "OPENAI_API_KEY",
    description: "OpenAI API key",
    required: false,
    environments: ["staging", "production"],
    rotationInterval: 180,
  },
  {
    key: "STRIPE_SECRET_KEY",
    description: "Stripe secret key",
    required: false,
    environments: ["staging", "production"],
    rotationInterval: 90,
  },
];

// In-memory storage for environment variables
const environmentVariables: EnvironmentVariable[] = [];

/**
 * Get environment variables for a specific environment
 */
export function getEnvironmentVariables(environment: string): EnvironmentVariable[] {
  return environmentVariables.filter((env) => env.environment === environment);
}

/**
 * Set environment variable
 */
export function setEnvironmentVariable(
  environment: string,
  key: string,
  value: string,
  encrypted = false,
  description?: string,
  updatedBy = "system"
): void {
  const existingIndex = environmentVariables.findIndex(
    (env) => env.environment === environment && env.key === key
  );

  const variable: EnvironmentVariable = {
    key,
    value: encrypted ? encryptValue(value) : value,
    encrypted,
    description,
    required: isRequiredVariable(key),
    environment,
    lastUpdated: new Date(),
    updatedBy,
  };

  if (existingIndex >= 0) {
    environmentVariables[existingIndex] = variable;
  } else {
    environmentVariables.push(variable);
  }

  console.log(`[ENV_MANAGER] Set variable ${key} for ${environment}`);
}

/**
 * Delete environment variable
 */
export function deleteEnvironmentVariable(environment: string, key: string): boolean {
  const index = environmentVariables.findIndex(
    (env) => env.environment === environment && env.key === key
  );

  if (index >= 0) {
    environmentVariables.splice(index, 1);
    console.log(`[ENV_MANAGER] Deleted variable ${key} from ${environment}`);
    return true;
  }

  return false;
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(environment: string): EnvironmentHealth {
  const template = configTemplates[0]; // Use default template
  const envVars = getEnvironmentVariables(environment);
  const envVarKeys = envVars.map((v) => v.key);

  const checks: EnvironmentHealth["checks"] = [];
  const missingVariables: string[] = [];
  const expiredSecrets: string[] = [];

  // Check required variables
  template.variables.forEach((templateVar) => {
    if (templateVar.required && !envVarKeys.includes(templateVar.key)) {
      missingVariables.push(templateVar.key);
      checks.push({
        name: `Required variable: ${templateVar.key}`,
        status: "fail",
        message: `Missing required environment variable: ${templateVar.key}`,
        lastCheck: new Date(),
      });
    } else if (envVarKeys.includes(templateVar.key)) {
      const envVar = envVars.find((v) => v.key === templateVar.key);

      // Validate format if pattern is provided
      if (templateVar.validation && envVar) {
        const regex = new RegExp(templateVar.validation);
        const isValid = regex.test(envVar.value);

        checks.push({
          name: `Format validation: ${templateVar.key}`,
          status: isValid ? "pass" : "fail",
          message: isValid
            ? `Variable ${templateVar.key} format is valid`
            : `Variable ${templateVar.key} format is invalid`,
          lastCheck: new Date(),
        });
      }
    }
  });

  // Check secret rotation
  secretConfigs.forEach((secret) => {
    if (secret.environments.includes(environment)) {
      const envVar = envVars.find((v) => v.key === secret.key);

      if (envVar && secret.rotationInterval) {
        const daysSinceUpdate = Math.floor(
          (Date.now() - envVar.lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSinceUpdate > secret.rotationInterval) {
          expiredSecrets.push(secret.key);
          checks.push({
            name: `Secret rotation: ${secret.key}`,
            status: "warning",
            message: `Secret ${secret.key} should be rotated (${daysSinceUpdate} days old)`,
            lastCheck: new Date(),
          });
        } else {
          checks.push({
            name: `Secret rotation: ${secret.key}`,
            status: "pass",
            message: `Secret ${secret.key} is up to date`,
            lastCheck: new Date(),
          });
        }
      }
    }
  });

  // Determine overall status
  const hasFailures = checks.some((check) => check.status === "fail");
  const hasWarnings = checks.some((check) => check.status === "warning");

  let status: "healthy" | "warning" | "critical";
  if (hasFailures) {
    status = "critical";
  } else if (hasWarnings) {
    status = "warning";
  } else {
    status = "healthy";
  }

  return {
    environment,
    status,
    checks,
    missingVariables,
    expiredSecrets,
    lastHealthCheck: new Date(),
  };
}

/**
 * Generate environment configuration from template
 */
export function generateEnvironmentConfig(
  environment: string,
  templateName = "remix-cloudflare-app"
): Record<string, string> {
  const template = configTemplates.find((t) => t.name === templateName);
  if (!template) {
    throw new Error(`Template ${templateName} not found`);
  }

  const config: Record<string, string> = {};

  template.variables.forEach((variable) => {
    if (variable.defaultValue) {
      config[variable.key] = variable.defaultValue;
    } else if (variable.required) {
      config[variable.key] = `<REQUIRED: ${variable.description}>`;
    } else {
      config[variable.key] = `<OPTIONAL: ${variable.description}>`;
    }
  });

  return config;
}

/**
 * Export environment configuration
 */
export function exportEnvironmentConfig(
  environment: string,
  format: "env" | "json" | "yaml" = "env"
): string {
  const envVars = getEnvironmentVariables(environment);

  switch (format) {
    case "env":
      return envVars
        .map((variable) => {
          const value = variable.encrypted ? "<ENCRYPTED>" : variable.value;
          const comment = variable.description ? `# ${variable.description}` : "";
          return `${comment ? comment + "\n" : ""}${variable.key}=${value}`;
        })
        .join("\n\n");

    case "json": {
      const jsonConfig: Record<string, any> = {};
      envVars.forEach((variable) => {
        jsonConfig[variable.key] = {
          value: variable.encrypted ? "<ENCRYPTED>" : variable.value,
          encrypted: variable.encrypted,
          description: variable.description,
          required: variable.required,
          lastUpdated: variable.lastUpdated,
        };
      });
      return JSON.stringify(jsonConfig, null, 2);
    }

    case "yaml":
      return envVars
        .map((variable) => {
          const value = variable.encrypted ? "<ENCRYPTED>" : variable.value;
          const comment = variable.description ? `  # ${variable.description}` : "";
          return `${variable.key}: "${value}"${comment}`;
        })
        .join("\n");

    default:
      throw new Error(`Unsupported format: ${format}`);
  }
}

/**
 * Import environment configuration
 */
export function importEnvironmentConfig(
  environment: string,
  configData: string,
  format: "env" | "json" = "env",
  updatedBy = "import"
): number {
  let imported = 0;

  try {
    if (format === "env") {
      const lines = configData.split("\n");

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith("#")) {
          const [key, ...valueParts] = trimmed.split("=");
          if (key && valueParts.length > 0) {
            const value = valueParts.join("=");
            setEnvironmentVariable(
              environment,
              key.trim(),
              value.trim(),
              false,
              undefined,
              updatedBy
            );
            imported++;
          }
        }
      }
    } else if (format === "json") {
      const config = JSON.parse(configData);

      for (const [key, data] of Object.entries(config)) {
        if (typeof data === "object" && data !== null && "value" in data) {
          const varData = data as any;
          setEnvironmentVariable(
            environment,
            key,
            varData.value,
            varData.encrypted || false,
            varData.description,
            updatedBy
          );
          imported++;
        } else if (typeof data === "string") {
          setEnvironmentVariable(environment, key, data, false, undefined, updatedBy);
          imported++;
        }
      }
    }

    console.log(`[ENV_MANAGER] Imported ${imported} variables for ${environment}`);
    return imported;
  } catch (error) {
    console.error(`[ENV_MANAGER] Failed to import config for ${environment}:`, error);
    throw error;
  }
}

/**
 * Get configuration templates
 */
export function getConfigTemplates(): ConfigTemplate[] {
  return [...configTemplates];
}

/**
 * Get secret configurations
 */
export function getSecretConfigs(): SecretConfig[] {
  return [...secretConfigs];
}

/**
 * Rotate secret
 */
export function rotateSecret(environment: string, key: string, newValue: string): boolean {
  const secretConfig = secretConfigs.find((s) => s.key === key);
  if (!secretConfig || !secretConfig.environments.includes(environment)) {
    return false;
  }

  setEnvironmentVariable(environment, key, newValue, true, secretConfig.description, "rotation");

  // Update rotation timestamp
  secretConfig.lastRotated = new Date();

  console.log(`[ENV_MANAGER] Rotated secret ${key} for ${environment}`);
  return true;
}

/**
 * Get environment health for all environments
 */
export function getAllEnvironmentHealth(): EnvironmentHealth[] {
  const environments = ["development", "staging", "production"];
  return environments.map((env) => validateEnvironmentConfig(env));
}

/**
 * Helper functions
 */
function isRequiredVariable(key: string): boolean {
  const template = configTemplates[0];
  const templateVar = template.variables.find((v) => v.key === key);
  return templateVar?.required || false;
}

function encryptValue(value: string): string {
  // Simple encryption simulation (in production, use proper encryption)
  return Buffer.from(value).toString("base64");
}

function decryptValue(encryptedValue: string): string {
  // Simple decryption simulation (in production, use proper decryption)
  return Buffer.from(encryptedValue, "base64").toString();
}

/**
 * Initialize default environment variables
 */
export function initializeDefaultEnvironments(): void {
  const environments = ["development", "staging", "production"];

  environments.forEach((env) => {
    // Set some default variables
    setEnvironmentVariable(env, "NODE_ENV", env === "development" ? "development" : "production");
    setEnvironmentVariable(
      env,
      "LOG_LEVEL",
      env === "development" ? "debug" : env === "staging" ? "info" : "warn"
    );
    setEnvironmentVariable(env, "RATE_LIMIT_ENABLED", "true");
    setEnvironmentVariable(env, "CACHE_TTL", "300");
  });

  console.log("[ENV_MANAGER] Initialized default environment variables");
}

// Note: Initialization moved to be called explicitly when needed
// to avoid global scope issues in Cloudflare Workers
// Call initializeDefaultEnvironments() explicitly in your application startup
