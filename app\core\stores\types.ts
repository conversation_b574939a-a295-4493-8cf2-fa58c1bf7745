// app/stores/types.ts
// 通用的 store 类型定义

export interface BaseStore {
  // 重置 store 到初始状态
  reset: () => void;
}

// Note: User types moved to auth-store.ts to avoid duplication

// UI 相关状态
export interface UIState extends BaseStore {
  sidebarOpen: boolean;
  notifications: Notification[];

  // Actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  addNotification: (notification: Omit<Notification, "id">) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number; // 自动消失时间（毫秒），0 表示不自动消失
}

// 购物车相关状态（示例）
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
}

export interface CartState extends BaseStore {
  items: CartItem[];
  total: number;
  isOpen: boolean;

  // Actions
  addItem: (item: Omit<CartItem, "quantity">) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  toggleCart: () => void;
  setCartOpen: (open: boolean) => void;
}

// 应用全局状态
export interface AppState extends BaseStore {
  isInitialized: boolean;
  isOnline: boolean;

  // Actions
  setInitialized: (initialized: boolean) => void;
  setOnline: (online: boolean) => void;
}
