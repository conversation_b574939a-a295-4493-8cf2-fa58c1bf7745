/**
 * Logout Route
 * Handles user logout using Lucia 3
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { lucia } from "~/core/auth/lucia.server";

export async function action({ request }: ActionFunctionArgs) {
  const sessionId = lucia.readSessionCookie(request.headers.get("Cookie") ?? "");
  
  if (sessionId) {
    await lucia.invalidateSession(sessionId);
  }
  
  const sessionCookie = lucia.createBlankSessionCookie();
  
  return redirect("/auth/login", {
    headers: {
      "Set-Cookie": sessionCookie.serialize()
    }
  });
}

export async function loader({ request }: LoaderFunctionArgs) {
  const sessionId = lucia.readSessionCookie(request.headers.get("Cookie") ?? "");
  
  if (sessionId) {
    await lucia.invalidateSession(sessionId);
  }
  
  const sessionCookie = lucia.createBlankSessionCookie();
  
  return redirect("/auth/login", {
    headers: {
      "Set-Cookie": sessionCookie.serialize()
    }
  });
}

// This component should never render as the loader/action redirects
export default function Logout() {
  return <div>Logging out...</div>;
}
